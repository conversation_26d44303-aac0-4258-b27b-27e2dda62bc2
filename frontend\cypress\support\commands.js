// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

import '@testing-library/cypress/add-commands';

// Custom command to check if WebSocket connection is established
Cypress.Commands.add('checkWebSocketConnection', (timeout = 10000) => {
  cy.window().then((win) => {
    return new Cypress.Promise((resolve, reject) => {
      // Check if WebSocket is connected in Redux store
      const checkConnection = () => {
        const state = win.store.getState();
        if (state.websocket && state.websocket.connected) {
          resolve(true);
        }
        return false;
      };

      // Try to check connection immediately
      if (checkConnection()) {
        return;
      }

      // Set up interval to check connection
      const interval = setInterval(() => {
        if (checkConnection()) {
          clearInterval(interval);
          clearTimeout(timeoutId);
        }
      }, 500);

      // Set up timeout
      const timeoutId = setTimeout(() => {
        clearInterval(interval);
        reject(new Error('WebSocket connection timed out'));
      }, timeout);
    });
  });
});

// Custom command to wait for resources to load
Cypress.Commands.add('waitForResources', (resources = [], timeout = 10000) => {
  const log = Cypress.log({
    name: 'waitForResources',
    displayName: 'Wait For Resources',
    message: resources.join(', '),
    autoEnd: false,
  });

  return cy.window({ log: false }).then(
    { timeout },
    (win) => {
      return new Cypress.Promise((resolve) => {
        const resourcesLoaded = [];

        // Function to check if all resources are loaded
        const checkResources = () => {
          const performance = win.performance;
          const entries = performance.getEntriesByType('resource');

          for (const resource of resources) {
            const found = entries.find((entry) => entry.name.includes(resource));
            if (found && !resourcesLoaded.includes(resource)) {
              resourcesLoaded.push(resource);
              log.set('consoleProps', () => ({
                'Resource': resource,
                'Duration': found.duration,
                'Size': found.transferSize,
              }));
            }
          }

          if (resourcesLoaded.length === resources.length) {
            log.snapshot();
            log.end();
            resolve();
            return true;
          }

          return false;
        };

        // Check immediately
        if (checkResources()) {
          return;
        }

        // Set up observer for future resources
        const observer = new PerformanceObserver((list) => {
          if (checkResources()) {
            observer.disconnect();
          }
        });

        observer.observe({ entryTypes: ['resource'] });

        // Set timeout
        setTimeout(() => {
          observer.disconnect();
          log.error('Timed out waiting for resources');
          log.end();
          resolve();
        }, timeout);
      });
    }
  );
});

// Custom command to test WebSocket functionality
Cypress.Commands.add('testWebSocketMessage', (message, expectedResponse, timeout = 10000) => {
  cy.window().then((win) => {
    return new Cypress.Promise((resolve, reject) => {
      // Function to send message
      const sendMessage = () => {
        // Check if WebSocket is available and connected
        if (win.store && win.store.dispatch) {
          // Dispatch action to send message
          win.store.dispatch({
            type: 'WS_SEND_MESSAGE',
            payload: message
          });
          return true;
        }
        return false;
      };

      // Function to check for response
      const checkResponse = () => {
        if (win.store) {
          const state = win.store.getState();
          if (state.messages && state.messages.length > 0) {
            // Check if any message matches the expected response
            const found = state.messages.find(msg =>
              (expectedResponse.type && msg.type === expectedResponse.type) ||
              (expectedResponse.data && msg.data === expectedResponse.data)
            );

            if (found) {
              resolve(found);
              return true;
            }
          }
        }
        return false;
      };

      // Try to send message
      if (!sendMessage()) {
        const interval = setInterval(() => {
          if (sendMessage()) {
            clearInterval(interval);
          }
        }, 500);

        setTimeout(() => {
          clearInterval(interval);
          reject(new Error('Failed to send WebSocket message'));
        }, timeout / 2);
      }

      // Check for response
      if (checkResponse()) {
        return;
      }

      const responseInterval = setInterval(() => {
        if (checkResponse()) {
          clearInterval(responseInterval);
          clearTimeout(timeoutId);
        }
      }, 500);

      const timeoutId = setTimeout(() => {
        clearInterval(responseInterval);
        reject(new Error('Timed out waiting for WebSocket response'));
      }, timeout);
    });
  });
});

// ===== APP BUILDER SPECIFIC COMMANDS =====

// Command to load a template
Cypress.Commands.add('loadTemplate', (templateName) => {
  cy.get('[data-testid="template-manager-button"]').click();
  cy.wait('@getTemplates');
  cy.get('[data-testid="template-card"]')
    .contains(templateName)
    .click();
  cy.get('[data-testid="load-template-button"]').click();
  cy.get('[data-testid="template-manager-modal"]').should('not.exist');
});

// Command to add a component
Cypress.Commands.add('addComponent', (componentType) => {
  cy.get(`[data-testid="add-${componentType}-component"]`).click();
  cy.get(`[data-testid="component-${componentType}"]`).should('exist');
});

// Command to select a component
Cypress.Commands.add('selectComponent', (componentType, index = 0) => {
  cy.get(`[data-testid="component-${componentType}"]`).eq(index).click();
  cy.get(`[data-testid="component-${componentType}"]`)
    .eq(index)
    .should('have.class', 'selected');
});

// Command to edit component property
Cypress.Commands.add('editProperty', (propertyName, value) => {
  cy.get(`[data-testid="property-${propertyName}-input"]`)
    .clear()
    .type(value);
});

// Command to export code
Cypress.Commands.add('exportCode', (format = 'react') => {
  cy.get('[data-testid="export-button"]').click();
  cy.get('[data-testid="export-format-select"]').select(format);
  cy.get('[data-testid="generate-code-button"]').click();
  cy.wait('@exportApp');
});

// Command to wait for app to be ready
Cypress.Commands.add('waitForAppReady', () => {
  cy.get('[data-testid="app-builder"]', { timeout: 10000 }).should('be.visible');
  cy.get('[data-testid="loading-spinner"]').should('not.exist');
});

// Command to simulate drag and drop
Cypress.Commands.add('dragAndDrop', (source, target) => {
  cy.get(source).trigger('dragstart');
  cy.get(target).trigger('dragover').trigger('drop');
  cy.get(source).trigger('dragend');
});

// Command to setup API intercepts
Cypress.Commands.add('setupApiIntercepts', () => {
  cy.intercept('GET', '/api/templates/', { fixture: 'templates.json' }).as('getTemplates');
  cy.intercept('POST', '/api/export/', { fixture: 'export-response.json' }).as('exportApp');
  cy.intercept('GET', '/api/components/', { fixture: 'components.json' }).as('getComponents');
  cy.intercept('POST', '/api/templates/', { statusCode: 201, body: { id: 999 } }).as('saveTemplate');
});

// Command to check code quality
Cypress.Commands.add('checkCodeQuality', () => {
  cy.get('[data-testid="code-preview"]').should('be.visible');
  cy.get('[data-testid="code-preview"]').should('contain', 'import React');
  cy.get('[data-testid="code-preview"]').should('contain', 'export default');
});

// Command to test responsive design
Cypress.Commands.add('testResponsive', () => {
  // Test mobile
  cy.viewport(375, 667);
  cy.get('[data-testid="app-canvas"]').should('be.visible');

  // Test tablet
  cy.viewport(768, 1024);
  cy.get('[data-testid="app-canvas"]').should('be.visible');

  // Test desktop
  cy.viewport(1280, 720);
  cy.get('[data-testid="app-canvas"]').should('be.visible');
});

// Command to verify component properties
Cypress.Commands.add('verifyComponentProps', (componentType, expectedProps) => {
  cy.get(`[data-testid="component-${componentType}"]`).first().click();

  Object.entries(expectedProps).forEach(([prop, value]) => {
    cy.get(`[data-testid="property-${prop}-input"]`).should('have.value', value);
  });
});

// Command to test keyboard navigation
Cypress.Commands.add('testKeyboardNavigation', () => {
  cy.get('body').tab();
  cy.focused().should('be.visible');

  // Continue tabbing through focusable elements
  for (let i = 0; i < 5; i++) {
    cy.focused().tab();
    cy.focused().should('be.visible');
  }
});

// Command to verify code syntax highlighting
Cypress.Commands.add('verifyCodeHighlighting', () => {
  cy.get('[data-testid="syntax-highlighter"]').should('be.visible');
  cy.get('[data-testid="syntax-highlighter"] .token').should('exist');
  cy.get('[data-testid="syntax-highlighter"] .keyword').should('exist');
});

// Command to test undo/redo functionality
Cypress.Commands.add('testUndoRedo', () => {
  // Make a change
  cy.addComponent('button');
  cy.get('[data-testid="component-button"]').should('have.length', 1);

  // Undo
  cy.get('[data-testid="undo-button"]').click();
  cy.get('[data-testid="component-button"]').should('have.length', 0);

  // Redo
  cy.get('[data-testid="redo-button"]').click();
  cy.get('[data-testid="component-button"]').should('have.length', 1);
});
