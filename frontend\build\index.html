<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><meta name="theme-color" content="#2563EB"><meta name="description" content="App Builder 201 - Build your application with minimal setup"><title>App Builder 201</title><link rel="icon" href="/favicon.ico" type="image/x-icon"><link rel="shortcut icon" href="/favicon.ico" type="image/x-icon"><link rel="apple-touch-icon" href="/logo192.png"><link rel="manifest" href="/manifest.json"><link rel="preload" href="/static/css/main.css" as="style"><link rel="stylesheet" href="/static/css/main.css"><style>/* Simple loading animation styles */
    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #2563EB;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }</style><script defer="defer" src="/static/js/runtime.9b2ea28c.js"></script><script defer="defer" src="/static/js/critical-vendor.3e00bc3c.js"></script><script defer="defer" src="/static/js/5108.249a5cb5.js"></script><script defer="defer" src="/static/js/9845.e109dd3a.js"></script><script defer="defer" src="/static/js/107.ad50cf10.js"></script><script defer="defer" src="/static/js/8980.b8d5dedf.js"></script><script defer="defer" src="/static/js/6434.70d037b4.js"></script><script defer="defer" src="/static/js/5101.722c18f4.js"></script><script defer="defer" src="/static/js/1025.29e7153e.js"></script><script defer="defer" src="/static/js/3976.558c3a3e.js"></script><script defer="defer" src="/static/js/9669.88c4e3a3.js"></script><script defer="defer" src="/static/js/276.099b688a.js"></script><script defer="defer" src="/static/js/720.25b7dc7c.js"></script><script defer="defer" src="/static/js/5435.122bdeb7.js"></script><script defer="defer" src="/static/js/1114.67c39b02.js"></script><script defer="defer" src="/static/js/7571.e4e9d4e3.js"></script><script defer="defer" src="/static/js/2518.8a1201e0.js"></script><script defer="defer" src="/static/js/9060.463b5228.js"></script><script defer="defer" src="/static/js/4772.8ef858e3.js"></script><script defer="defer" src="/static/js/7449.1d21d940.js"></script><script defer="defer" src="/static/js/8771.a7d25479.js"></script><script defer="defer" src="/static/js/9843.1b43387f.js"></script><script defer="defer" src="/static/js/9832.47eecb86.js"></script><script defer="defer" src="/static/js/2698.ec7f10da.js"></script><script defer="defer" src="/static/js/5124.7f938f81.js"></script><script defer="defer" src="/static/js/1629.eb65cd83.js"></script><script defer="defer" src="/static/js/8287.87410f71.js"></script><script defer="defer" src="/static/js/1486.c8a18cde.js"></script><script defer="defer" src="/static/js/2675.5911cef8.js"></script><script defer="defer" src="/static/js/9907.40d968b4.js"></script><script defer="defer" src="/static/js/7192.7ca05dc8.js"></script><script defer="defer" src="/static/js/9372.3d23e4c8.js"></script><script defer="defer" src="/static/js/4802.63c10a3f.js"></script><script defer="defer" src="/static/js/1807.0ad2d898.js"></script><script defer="defer" src="/static/js/747.a8e5fd4e.js"></script><script defer="defer" src="/static/js/7088.d8b95dc1.js"></script><script defer="defer" src="/static/js/8278.9af8fc30.js"></script><script defer="defer" src="/static/js/6059.1bd3bcb2.js"></script><script defer="defer" src="/static/js/8346.ad3b1028.js"></script><script defer="defer" src="/static/js/687.b171d465.js"></script><script defer="defer" src="/static/js/2036.89f8d3c4.js"></script><script defer="defer" src="/static/js/4488.585ad694.js"></script><script defer="defer" src="/static/js/7496.867acca7.js"></script><script defer="defer" src="/static/js/4447.51817473.js"></script><script defer="defer" src="/static/js/1889.9fd6af10.js"></script><script defer="defer" src="/static/js/2773.31fe48be.js"></script><script defer="defer" src="/static/js/6110.d5ead66c.js"></script><script defer="defer" src="/static/js/6037.1be056d4.js"></script><script defer="defer" src="/static/js/3357.e82e14bc.js"></script><script defer="defer" src="/static/js/2272.6cea77bf.js"></script><script defer="defer" src="/static/js/895.f042adc5.js"></script><script defer="defer" src="/static/js/1115.db271327.js"></script><script defer="defer" src="/static/js/3205.1a70db9c.js"></script><script defer="defer" src="/static/js/7056.3b0629ca.js"></script><script defer="defer" src="/static/js/3385.bedd571d.js"></script><script defer="defer" src="/static/js/6261.be21bebf.js"></script><script defer="defer" src="/static/js/8020.c2bd5348.js"></script><script defer="defer" src="/static/js/2665.c8a103e2.js"></script><script defer="defer" src="/static/js/8104.3c9c8155.js"></script><script defer="defer" src="/static/js/9225.820467f5.js"></script><script defer="defer" src="/static/js/1390.c56b7c1a.js"></script><script defer="defer" src="/static/js/8589.972ecb55.js"></script><script defer="defer" src="/static/js/3230.4f376ce3.js"></script><script defer="defer" src="/static/js/9919.67f3cceb.js"></script><script defer="defer" src="/static/js/2378.88876f37.js"></script><script defer="defer" src="/static/js/939.a53318f6.js"></script><script defer="defer" src="/static/js/6082.316462cf.js"></script><script defer="defer" src="/static/js/551.fd3c454a.js"></script><script defer="defer" src="/static/js/1735.ac4f8bd4.js"></script><script defer="defer" src="/static/js/38.d8d93798.js"></script><script defer="defer" src="/static/js/5588.beabfb51.js"></script><script defer="defer" src="/static/js/1387.c2876090.js"></script><script defer="defer" src="/static/js/5955.86e38a07.js"></script><script defer="defer" src="/static/js/7419.7cf85034.js"></script><script defer="defer" src="/static/js/main.4f064d56.27c60495.js"></script><script defer="defer" src="/static/js/main.84781932.5b001085.js"></script><script defer="defer" src="/static/js/main.389e2277.d052a2f1.js"></script><link href="/static/css/main.e96e9bea.7234891a.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"><div class="loading-container"><div class="loading-spinner"></div><h2>Loading App Builder 201...</h2><p>Preparing your development environment</p></div></div><script>if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
          .then(registration => {
            console.log('Service Worker registered with scope:', registration.scope);

            // Check for updates
            registration.addEventListener('updatefound', () => {
              const newWorker = registration.installing;
              console.log('Service Worker update found!');

              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content is available, show notification
                  if (window.confirm('New version available! Reload to update?')) {
                    window.location.reload();
                  }
                }
              });
            });
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error);
          });

        // Handle service worker updates
        let refreshing = false;
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          if (!refreshing) {
            refreshing = true;
            window.location.reload();
          }
        });
      });
    }</script><script>// Load test script in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      console.log('🧪 Loading App Builder test script...');

      // Simple test function that can be run in console
      window.testAppBuilderBasic = function () {
        console.log('🧪 Testing App Builder functionality...');

        const results = {
          reactLoaded: typeof window.React !== 'undefined',
          appLoaded: window.__APP_LOADED__ || false,
          hasComponents: document.body.textContent.includes('Components'),
          hasCanvas: document.body.textContent.includes('Canvas') || document.body.textContent.includes('Preview'),
          hasButtons: document.querySelectorAll('button').length > 0
        };

        console.log('📊 Test Results:');
        console.log('React Loaded:', results.reactLoaded ? '✅' : '❌');
        console.log('App Loaded:', results.appLoaded ? '✅' : '❌');
        console.log('Has Components Section:', results.hasComponents ? '✅' : '❌');
        console.log('Has Canvas/Preview Area:', results.hasCanvas ? '✅' : '❌');
        console.log('Has Interactive Buttons:', results.hasButtons ? '✅' : '❌');

        const success = Object.values(results).every(Boolean);
        console.log('🎯 Overall Status:', success ? '✅ PASS' : '❌ FAIL');

        return results;
      };

      // Auto-run test after a delay
      setTimeout(() => {
        if (typeof window.testAppBuilderBasic === 'function') {
          window.testAppBuilderBasic();
        }
      }, 3000);
    }</script></body></html>