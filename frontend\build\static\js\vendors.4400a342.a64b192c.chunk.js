"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8244],{

/***/ 719:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(29768);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  oneC: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("oneC", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 32402, 23));
  }),
  abnf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("abnf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52701, 23));
  }),
  accesslog: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("accesslog", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 35076, 23));
  }),
  actionscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("actionscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 49115, 23));
  }),
  ada: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ada", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 99428, 23));
  }),
  angelscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("angelscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 57246, 23));
  }),
  apache: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("apache", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 29980, 23));
  }),
  applescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("applescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 42731, 23));
  }),
  arcade: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("arcade", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 18206, 23));
  }),
  arduino: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("arduino", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 27220, 23));
  }),
  armasm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("armasm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 79139, 23));
  }),
  asciidoc: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("asciidoc", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 49405, 23));
  }),
  aspectj: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("aspectj", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 36478, 23));
  }),
  autohotkey: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("autohotkey", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 62923, 23));
  }),
  autoit: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("autoit", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 77556, 23));
  }),
  avrasm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("avrasm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 13732, 23));
  }),
  awk: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("awk", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 19277, 23));
  }),
  axapta: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("axapta", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 77909, 23));
  }),
  bash: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bash", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 35344, 23));
  }),
  basic: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("basic", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 6722, 23));
  }),
  bnf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bnf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 75610, 23));
  }),
  brainfuck: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("brainfuck", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 25439, 23));
  }),
  cLike: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cLike", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 97053, 23));
  }),
  c: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("c", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 39497, 23));
  }),
  cal: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cal", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 67040, 23));
  }),
  capnproto: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("capnproto", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 38055, 23));
  }),
  ceylon: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ceylon", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 3838, 23));
  }),
  clean: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("clean", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 78333, 23));
  }),
  clojureRepl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("clojureRepl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 5892, 23));
  }),
  clojure: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("clojure", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 80364, 23));
  }),
  cmake: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cmake", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 56427, 23));
  }),
  coffeescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("coffeescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 64125, 23));
  }),
  coq: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("coq", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 39081, 23));
  }),
  cos: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cos", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30279, 23));
  }),
  cpp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cpp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 29721, 23));
  }),
  crmsh: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("crmsh", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 67015, 23));
  }),
  crystal: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("crystal", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 33270, 23));
  }),
  csharp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("csharp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 88425, 23));
  }),
  csp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("csp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 82086, 23));
  }),
  css: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("css", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 53315, 23));
  }),
  d: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("d", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 91036, 23));
  }),
  dart: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dart", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 58811, 23));
  }),
  delphi: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("delphi", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 61870, 23));
  }),
  diff: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("diff", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 6033, 23));
  }),
  django: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("django", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 35133, 23));
  }),
  dns: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dns", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 49927, 23));
  }),
  dockerfile: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dockerfile", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 26756, 23));
  }),
  dos: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dos", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 55754, 23));
  }),
  dsconfig: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dsconfig", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 91533, 23));
  }),
  dts: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dts", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 39129, 23));
  }),
  dust: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dust", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 14058, 23));
  }),
  ebnf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ebnf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 44177, 23));
  }),
  elixir: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("elixir", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81297, 23));
  }),
  elm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("elm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 42292, 23));
  }),
  erb: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("erb", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 84663, 23));
  }),
  erlangRepl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("erlangRepl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81983, 23));
  }),
  erlang: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("erlang", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 945, 23));
  }),
  excel: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("excel", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 58451, 23));
  }),
  fix: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("fix", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7003, 23));
  }),
  flix: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("flix", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24701, 23));
  }),
  fortran: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("fortran", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30258, 23));
  }),
  fsharp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("fsharp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 94108, 23));
  }),
  gams: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gams", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 48126, 23));
  }),
  gauss: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gauss", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 25117, 23));
  }),
  gcode: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gcode", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 19156, 23));
  }),
  gherkin: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gherkin", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 13666, 23));
  }),
  glsl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("glsl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 19614, 23));
  }),
  gml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 2904, 23));
  }),
  go: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("go", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 44564, 23));
  }),
  golo: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("golo", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 14205, 23));
  }),
  gradle: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gradle", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 29789, 23));
  }),
  groovy: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("groovy", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 72896, 23));
  }),
  haml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("haml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 57292, 23));
  }),
  handlebars: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("handlebars", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 18514, 23));
  }),
  haskell: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("haskell", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 34904, 23));
  }),
  haxe: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("haxe", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 58670, 23));
  }),
  hsp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("hsp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 74060, 23));
  }),
  htmlbars: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("htmlbars", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 22329, 23));
  }),
  http: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("http", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 73402, 23));
  }),
  hy: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("hy", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45617, 23));
  }),
  inform7: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("inform7", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 12962, 23));
  }),
  ini: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ini", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 40634, 23));
  }),
  irpf90: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("irpf90", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 76414, 23));
  }),
  isbl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("isbl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 1012, 23));
  }),
  java: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("java", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81786, 23));
  }),
  javascript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("javascript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 95089, 23));
  }),
  jbossCli: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jbossCli", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 39040, 23));
  }),
  json: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("json", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 65772, 23));
  }),
  juliaRepl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("juliaRepl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 3231, 23));
  }),
  julia: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("julia", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 27601, 23));
  }),
  kotlin: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("kotlin", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 99559, 23));
  }),
  lasso: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("lasso", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 31942, 23));
  }),
  latex: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("latex", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 8100, 23));
  }),
  ldif: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ldif", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 86685, 23));
  }),
  leaf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("leaf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 98186, 23));
  }),
  less: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("less", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52539, 23));
  }),
  lisp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("lisp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 73458, 23));
  }),
  livecodeserver: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("livecodeserver", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 75814, 23));
  }),
  livescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("livescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 3035, 23));
  }),
  llvm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("llvm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 33543, 23));
  }),
  lsl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("lsl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 3399, 23));
  }),
  lua: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("lua", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 32670, 23));
  }),
  makefile: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("makefile", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 59934, 23));
  }),
  markdown: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("markdown", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 96503, 23));
  }),
  mathematica: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mathematica", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 55672, 23));
  }),
  matlab: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("matlab", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 56023, 23));
  }),
  maxima: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("maxima", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 29433, 23));
  }),
  mel: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mel", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 68254, 23));
  }),
  mercury: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mercury", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 48557, 23));
  }),
  mipsasm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mipsasm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24714, 23));
  }),
  mizar: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mizar", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 91788, 23));
  }),
  mojolicious: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mojolicious", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 90593, 23));
  }),
  monkey: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("monkey", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 29387, 23));
  }),
  moonscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("moonscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 75468, 23));
  }),
  n1ql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("n1ql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52962, 23));
  }),
  nginx: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nginx", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 4896, 23));
  }),
  nim: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nim", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 63712, 23));
  }),
  nix: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nix", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 34659, 23));
  }),
  nodeRepl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nodeRepl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 26122, 23));
  }),
  nsis: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nsis", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 64561, 23));
  }),
  objectivec: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("objectivec", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 96494, 23));
  }),
  ocaml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ocaml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 60108, 23));
  }),
  openscad: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("openscad", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 89689, 23));
  }),
  oxygene: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("oxygene", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 78327, 23));
  }),
  parser3: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("parser3", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52052, 23));
  }),
  perl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("perl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 59272, 23));
  }),
  pf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 9412, 23));
  }),
  pgsql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pgsql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 80431, 23));
  }),
  phpTemplate: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("phpTemplate", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 85579, 23));
  }),
  php: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("php", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 34608, 23));
  }),
  plaintext: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("plaintext", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7939, 23));
  }),
  pony: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pony", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 39350, 23));
  }),
  powershell: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("powershell", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 26571, 23));
  }),
  processing: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("processing", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 50181, 23));
  }),
  profile: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("profile", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 46269, 23));
  }),
  prolog: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("prolog", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 3105, 23));
  }),
  properties: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("properties", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 2356, 23));
  }),
  protobuf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("protobuf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 25093, 23));
  }),
  puppet: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("puppet", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 69104, 23));
  }),
  purebasic: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("purebasic", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 94412, 23));
  }),
  pythonRepl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pythonRepl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 90736, 23));
  }),
  python: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("python", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 87192, 23));
  }),
  q: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("q", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 23255, 23));
  }),
  qml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("qml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 55506, 23));
  }),
  r: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("r", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 22698, 23));
  }),
  reasonml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("reasonml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 15331, 23));
  }),
  rib: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("rib", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24745, 23));
  }),
  roboconf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("roboconf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 75812, 23));
  }),
  routeros: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("routeros", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 53557, 23));
  }),
  rsl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("rsl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 53409, 23));
  }),
  ruby: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ruby", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 82978, 23));
  }),
  ruleslanguage: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ruleslanguage", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 78385, 23));
  }),
  rust: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("rust", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 39688, 23));
  }),
  sas: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sas", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7005, 23));
  }),
  scala: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("scala", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 43618, 23));
  }),
  scheme: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("scheme", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 36617, 23));
  }),
  scilab: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("scilab", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 92970, 23));
  }),
  scss: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("scss", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 3206, 23));
  }),
  shell: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("shell", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 77918, 23));
  }),
  smali: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("smali", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 91180, 23));
  }),
  smalltalk: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("smalltalk", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 72241, 23));
  }),
  sml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 4460, 23));
  }),
  sqf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sqf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7186, 23));
  }),
  sql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 76384, 23));
  }),
  sqlMore: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sqlMore", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30414, 23));
  }),
  stan: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("stan", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24490, 23));
  }),
  stata: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("stata", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81497, 23));
  }),
  step21: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("step21", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 36809, 23));
  }),
  stylus: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("stylus", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 62224, 23));
  }),
  subunit: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("subunit", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 95976, 23));
  }),
  swift: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("swift", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 94635, 23));
  }),
  taggerscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("taggerscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 98199, 23));
  }),
  tap: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("tap", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 58713, 23));
  }),
  tcl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("tcl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 41519, 23));
  }),
  thrift: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("thrift", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45449, 23));
  }),
  tp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("tp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 88454, 23));
  }),
  twig: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("twig", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 23195, 23));
  }),
  typescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("typescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 62825, 23));
  }),
  vala: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vala", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 55320, 23));
  }),
  vbnet: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vbnet", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 50959, 23));
  }),
  vbscriptHtml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vbscriptHtml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 88103, 23));
  }),
  vbscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vbscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 4893, 23));
  }),
  verilog: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("verilog", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 57522, 23));
  }),
  vhdl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vhdl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 37492, 23));
  }),
  vim: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vim", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 1192, 23));
  }),
  x86asm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("x86asm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 53447, 23));
  }),
  xl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("xl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 15790, 23));
  }),
  xml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("xml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 17285, 23));
  }),
  xquery: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("xquery", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 78130, 23));
  }),
  yaml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("yaml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 17533, 23));
  }),
  zephir: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("zephir", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 57204, 23));
  })
});

/***/ }),

/***/ 29768:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);


function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = "function" == typeof Symbol ? Symbol : {}, a = i.iterator || "@@iterator", c = i.asyncIterator || "@@asyncIterator", u = i.toStringTag || "@@toStringTag"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, ""); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, "_invoke", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: "normal", arg: t.call(e, r) }; } catch (t) { return { type: "throw", arg: t }; } } e.wrap = wrap; var h = "suspendedStart", l = "suspendedYield", f = "executing", s = "completed", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { ["next", "throw", "return"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if ("throw" !== c.type) { var u = c.arg, h = u.value; return h && "object" == (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(h) && n.call(h, "__await") ? e.resolve(h.__await).then(function (t) { invoke("next", t, i, a); }, function (t) { invoke("throw", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke("throw", t, i, a); }); } a(c.arg); } var r; o(this, "_invoke", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error("Generator is already running"); if (o === s) { if ("throw" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if ("next" === n.method) n.sent = n._sent = n.arg;else if ("throw" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else "return" === n.method && n.abrupt("return", n.arg); o = f; var p = tryCatch(e, r, n); if ("normal" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } "throw" === p.type && (o = s, n.method = "throw", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, "throw" === n && e.iterator["return"] && (r.method = "return", r.arg = t, maybeInvokeDelegate(e, r), "throw" === r.method) || "return" !== n && (r.method = "throw", r.arg = new TypeError("The iterator does not provide a '" + n + "' method")), y; var i = tryCatch(o, e.iterator, r.arg); if ("throw" === i.type) return r.method = "throw", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, "return" !== r.method && (r.method = "next", r.arg = t), r.delegate = null, y) : a : (r.method = "throw", r.arg = new TypeError("iterator result is not an object"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = "normal", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: "root" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || "" === e) { var r = e[a]; if (r) return r.call(e); if ("function" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e) + " is not iterable"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, "GeneratorFunction"), e.isGeneratorFunction = function (t) { var e = "function" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || "GeneratorFunction" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, "GeneratorFunction")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, "Generator"), define(g, a, function () { return this; }), define(g, "toString", function () { return "[object Generator]"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = "next", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) "t" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if ("throw" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = "throw", a.arg = e, r.next = n, o && (r.method = "next", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if ("root" === i.tryLoc) return handle("end"); if (i.tryLoc <= this.prev) { var c = n.call(i, "catchLoc"), u = n.call(i, "finallyLoc"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error("try statement without catch or finally"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, "finallyLoc") && this.prev < o.finallyLoc) { var i = o; break; } } i && ("break" === t || "continue" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = "next", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if ("throw" === t.type) throw t.arg; return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, "catch": function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if ("throw" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error("illegal catch attempt"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, "next" === this.method && (this.arg = t), y; } }, e; }
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (function (name, loader) {
  return /*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(registerLanguage) {
      var module;
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return loader();
          case 2:
            module = _context.sent;
            registerLanguage(name, module["default"] || module);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }();
});

/***/ }),

/***/ 42871:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(29768);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  abap: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("abap", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 37702, 23));
  }),
  abnf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("abnf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 75695, 23));
  }),
  actionscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("actionscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 41241, 23));
  }),
  ada: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ada", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 14502, 23));
  }),
  agda: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("agda", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 80003, 23));
  }),
  al: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("al", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 4977, 23));
  }),
  antlr4: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("antlr4", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52681, 23));
  }),
  apacheconf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("apacheconf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 54196, 23));
  }),
  apex: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("apex", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 50940, 23));
  }),
  apl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("apl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 87341, 23));
  }),
  applescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("applescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 10793, 23));
  }),
  aql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("aql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 78672, 23));
  }),
  arduino: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("arduino", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 44778, 23));
  }),
  arff: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("arff", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 96583, 23));
  }),
  asciidoc: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("asciidoc", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 25355, 23));
  }),
  asm6502: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("asm6502", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 28382, 23));
  }),
  asmatmel: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("asmatmel", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 94912, 23));
  }),
  aspnet: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("aspnet", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 77223, 23));
  }),
  autohotkey: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("autohotkey", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 5445, 23));
  }),
  autoit: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("autoit", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 77142, 23));
  }),
  avisynth: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("avisynth", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 37376, 23));
  }),
  avroIdl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("avroIdl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 18138, 23));
  }),
  bash: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bash", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 43622, 23));
  }),
  basic: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("basic", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7264, 23));
  }),
  batch: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("batch", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 67478, 23));
  }),
  bbcode: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bbcode", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 60101, 23));
  }),
  bicep: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bicep", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 3877, 23));
  }),
  birb: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("birb", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 43285, 23));
  }),
  bison: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bison", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 87917, 23));
  }),
  bnf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bnf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 88800, 23));
  }),
  brainfuck: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("brainfuck", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 59657, 23));
  }),
  brightscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("brightscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 76599, 23));
  }),
  bro: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bro", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 92091, 23));
  }),
  bsl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("bsl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 33611, 23));
  }),
  c: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("c", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 31687, 23));
  }),
  cfscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cfscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 2370, 23));
  }),
  chaiscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("chaiscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24286, 23));
  }),
  cil: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cil", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 40214, 23));
  }),
  clike: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("clike", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 93248, 23));
  }),
  clojure: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("clojure", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 14054, 23));
  }),
  cmake: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cmake", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45477, 23));
  }),
  cobol: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cobol", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 80025, 23));
  }),
  coffeescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("coffeescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81687, 23));
  }),
  concurnas: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("concurnas", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30454, 23));
  }),
  coq: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("coq", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 32939, 23));
  }),
  cpp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cpp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24519, 23));
  }),
  crystal: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("crystal", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 75548, 23));
  }),
  csharp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("csharp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 26523, 23));
  }),
  cshtml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cshtml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7211, 23));
  }),
  csp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("csp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 51160, 23));
  }),
  cssExtras: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cssExtras", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 11149, 23));
  }),
  css: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("css", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 49137, 23));
  }),
  csv: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("csv", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 33934, 23));
  }),
  cypher: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("cypher", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 73183, 23));
  }),
  d: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("d", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 60422, 23));
  }),
  dart: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dart", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 84125, 23));
  }),
  dataweave: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dataweave", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 67012, 23));
  }),
  dax: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dax", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 89155, 23));
  }),
  dhall: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dhall", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 58245, 23));
  }),
  diff: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("diff", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 12551, 23));
  }),
  django: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("django", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 63507, 23));
  }),
  dnsZoneFile: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dnsZoneFile", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 23507, 23));
  }),
  docker: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("docker", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 76064, 23));
  }),
  dot: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("dot", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 56489, 23));
  }),
  ebnf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ebnf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 84771, 23));
  }),
  editorconfig: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("editorconfig", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 21711, 23));
  }),
  eiffel: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("eiffel", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 53231, 23));
  }),
  ejs: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ejs", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 79962, 23));
  }),
  elixir: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("elixir", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 51371, 23));
  }),
  elm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("elm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 53558, 23));
  }),
  erb: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("erb", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 3057, 23));
  }),
  erlang: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("erlang", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 22339, 23));
  }),
  etlua: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("etlua", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 47603, 23));
  }),
  excelFormula: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("excelFormula", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 42986, 23));
  }),
  factor: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("factor", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 90027, 23));
  }),
  falselang: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("falselang", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30325, 23));
  }),
  firestoreSecurityRules: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("firestoreSecurityRules", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 79584, 23));
  }),
  flow: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("flow", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 69820, 23));
  }),
  fortran: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("fortran", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 67224, 23));
  }),
  fsharp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("fsharp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 4302, 23));
  }),
  ftl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ftl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 14692, 23));
  }),
  gap: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gap", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 16678, 23));
  }),
  gcode: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gcode", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 66842, 23));
  }),
  gdscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gdscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 67656, 23));
  }),
  gedcom: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gedcom", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81369, 23));
  }),
  gherkin: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gherkin", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 96268, 23));
  }),
  git: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("git", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24274, 23));
  }),
  glsl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("glsl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 31284, 23));
  }),
  gml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 44014, 23));
  }),
  gn: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("gn", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 15137, 23));
  }),
  goModule: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("goModule", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45259, 23));
  }),
  go: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("go", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7858, 23));
  }),
  graphql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("graphql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 88355, 23));
  }),
  groovy: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("groovy", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 72922, 23));
  }),
  haml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("haml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 9322, 23));
  }),
  handlebars: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("handlebars", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 4416, 23));
  }),
  haskell: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("haskell", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 55749, 23));
  }),
  haxe: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("haxe", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 42188, 23));
  }),
  hcl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("hcl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 78137, 23));
  }),
  hlsl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("hlsl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 911, 23));
  }),
  hoon: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("hoon", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 73776, 23));
  }),
  hpkp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("hpkp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 18359, 23));
  }),
  hsts: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("hsts", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 20116, 23));
  }),
  http: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("http", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 12760, 23));
  }),
  ichigojam: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ichigojam", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 973, 23));
  }),
  icon: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("icon", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 83365, 23));
  }),
  icuMessageFormat: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("icuMessageFormat", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 48559, 23));
  }),
  idris: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("idris", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 96387, 23));
  }),
  iecst: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("iecst", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 33298, 23));
  }),
  ignore: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ignore", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 1970, 23));
  }),
  inform7: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("inform7", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 72779, 23));
  }),
  ini: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ini", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 97888, 23));
  }),
  io: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("io", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 35136, 23));
  }),
  j: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("j", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 32728, 23));
  }),
  java: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("java", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 38072, 23));
  }),
  javadoc: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("javadoc", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 46716, 23));
  }),
  javadoclike: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("javadoclike", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 22995, 23));
  }),
  javascript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("javascript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 19283, 23));
  }),
  javastacktrace: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("javastacktrace", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52289, 23));
  }),
  jexl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jexl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 72169, 23));
  }),
  jolie: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jolie", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 8685, 23));
  }),
  jq: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jq", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 53481, 23));
  }),
  jsExtras: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jsExtras", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 23935, 23));
  }),
  jsTemplates: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jsTemplates", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 14085, 23));
  }),
  jsdoc: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jsdoc", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81401, 23));
  }),
  json: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("json", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 46506, 23));
  }),
  json5: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("json5", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 63219, 23));
  }),
  jsonp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jsonp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 80064, 23));
  }),
  jsstacktrace: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jsstacktrace", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 12906, 23));
  }),
  jsx: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("jsx", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 48535, 23));
  }),
  julia: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("julia", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 10599, 23));
  }),
  keepalived: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("keepalived", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 48846, 23));
  }),
  keyman: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("keyman", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 93641, 23));
  }),
  kotlin: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("kotlin", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 84737, 23));
  }),
  kumir: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("kumir", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 35310, 23));
  }),
  kusto: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("kusto", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 54502, 23));
  }),
  latex: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("latex", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81526, 23));
  }),
  latte: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("latte", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 1720, 23));
  }),
  less: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("less", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 14081, 23));
  }),
  lilypond: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("lilypond", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 5887, 23));
  }),
  liquid: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("liquid", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 58776, 23));
  }),
  lisp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("lisp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 35100, 23));
  }),
  livescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("livescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 15673, 23));
  }),
  llvm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("llvm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 57377, 23));
  }),
  log: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("log", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 91800, 23));
  }),
  lolcode: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("lolcode", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 8736, 23));
  }),
  lua: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("lua", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 50347, 23));
  }),
  magma: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("magma", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 59739, 23));
  }),
  makefile: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("makefile", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 80460, 23));
  }),
  markdown: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("markdown", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52177, 23));
  }),
  markupTemplating: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("markupTemplating", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 56876, 23));
  }),
  markup: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("markup", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 49264, 23));
  }),
  matlab: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("matlab", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 6593, 23));
  }),
  maxscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("maxscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45867, 23));
  }),
  mel: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mel", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 16136, 23));
  }),
  mermaid: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mermaid", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 13217, 23));
  }),
  mizar: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mizar", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 72281, 23));
  }),
  mongodb: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("mongodb", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 77396, 23));
  }),
  monkey: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("monkey", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 64365, 23));
  }),
  moonscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("moonscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 37250, 23));
  }),
  n1ql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("n1ql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 48236, 23));
  }),
  n4js: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("n4js", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 37033, 23));
  }),
  nand2tetrisHdl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nand2tetrisHdl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24489, 23));
  }),
  naniscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("naniscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 42455, 23));
  }),
  nasm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nasm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 33853, 23));
  }),
  neon: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("neon", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 92972, 23));
  }),
  nevod: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nevod", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 95152, 23));
  }),
  nginx: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nginx", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 40550, 23));
  }),
  nim: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nim", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 82574, 23));
  }),
  nix: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nix", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 35233, 23));
  }),
  nsis: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("nsis", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 85747, 23));
  }),
  objectivec: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("objectivec", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30940, 23));
  }),
  ocaml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ocaml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 94334, 23));
  }),
  opencl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("opencl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 1719, 23));
  }),
  openqasm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("openqasm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 83226, 23));
  }),
  oz: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("oz", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 41365, 23));
  }),
  parigp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("parigp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 13917, 23));
  }),
  parser: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("parser", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 16203, 23));
  }),
  pascal: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pascal", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 1384, 23));
  }),
  pascaligo: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pascaligo", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 79531, 23));
  }),
  pcaxis: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pcaxis", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 54056, 23));
  }),
  peoplecode: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("peoplecode", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30662, 23));
  }),
  perl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("perl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 41565, 23));
  }),
  phpExtras: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("phpExtras", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 64216, 23));
  }),
  php: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("php", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 87642, 23));
  }),
  phpdoc: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("phpdoc", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 65546, 23));
  }),
  plsql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("plsql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 13978, 23));
  }),
  powerquery: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("powerquery", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 48839, 23));
  }),
  powershell: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("powershell", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 36849, 23));
  }),
  processing: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("processing", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 94559, 23));
  }),
  prolog: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("prolog", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 58031, 23));
  }),
  promql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("promql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 44359, 23));
  }),
  properties: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("properties", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 25549, 23));
  }),
  protobuf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("protobuf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 95239, 23));
  }),
  psl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("psl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 24321, 23));
  }),
  pug: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pug", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 66554, 23));
  }),
  puppet: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("puppet", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 87726, 23));
  }),
  pure: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("pure", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 88134, 23));
  }),
  purebasic: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("purebasic", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 64806, 23));
  }),
  purescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("purescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 5605, 23));
  }),
  python: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("python", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 73870, 23));
  }),
  q: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("q", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 54809, 23));
  }),
  qml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("qml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 91724, 23));
  }),
  qore: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("qore", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 47930, 23));
  }),
  qsharp: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("qsharp", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 35865, 23));
  }),
  r: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("r", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 43808, 23));
  }),
  racket: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("racket", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 95518, 23));
  }),
  reason: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("reason", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 92684, 23));
  }),
  regex: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("regex", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45986, 23));
  }),
  rego: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("rego", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 983, 23));
  }),
  renpy: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("renpy", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 23588, 23));
  }),
  rest: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("rest", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 56278, 23));
  }),
  rip: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("rip", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 44853, 23));
  }),
  roboconf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("roboconf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52746, 23));
  }),
  robotframework: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("robotframework", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7068, 23));
  }),
  ruby: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("ruby", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 54264, 23));
  }),
  rust: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("rust", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 41318, 23));
  }),
  sas: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sas", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 65175, 23));
  }),
  sass: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sass", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 16354, 23));
  }),
  scala: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("scala", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45223, 23));
  }),
  scheme: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("scheme", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45087, 23));
  }),
  scss: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("scss", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 72388, 23));
  }),
  shellSession: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("shellSession", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 29813, 23));
  }),
  smali: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("smali", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 74394, 23));
  }),
  smalltalk: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("smalltalk", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 51315, 23));
  }),
  smarty: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("smarty", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 65868, 23));
  }),
  sml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 42770, 23));
  }),
  solidity: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("solidity", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 90969, 23));
  }),
  solutionFile: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("solutionFile", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30230, 23));
  }),
  soy: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("soy", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 78035, 23));
  }),
  sparql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sparql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 17553, 23));
  }),
  splunkSpl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("splunkSpl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 73243, 23));
  }),
  sqf: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sqf", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 64364, 23));
  }),
  sql: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("sql", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 95422, 23));
  }),
  squirrel: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("squirrel", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30007, 23));
  }),
  stan: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("stan", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 17280, 23));
  }),
  stylus: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("stylus", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 81758, 23));
  }),
  swift: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("swift", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 90657, 23));
  }),
  systemd: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("systemd", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 21573, 23));
  }),
  t4Cs: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("t4Cs", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 70759, 23));
  }),
  t4Templating: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("t4Templating", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 83940, 23));
  }),
  t4Vb: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("t4Vb", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 23037, 23));
  }),
  tap: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("tap", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 89275, 23));
  }),
  tcl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("tcl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 30949, 23));
  }),
  textile: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("textile", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 77299, 23));
  }),
  toml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("toml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 84508, 23));
  }),
  tremor: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("tremor", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 26329, 23));
  }),
  tsx: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("tsx", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 8553, 23));
  }),
  tt2: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("tt2", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 71456, 23));
  }),
  turtle: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("turtle", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 36322, 23));
  }),
  twig: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("twig", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 58145, 23));
  }),
  typescript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("typescript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 50715, 23));
  }),
  typoscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("typoscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 12777, 23));
  }),
  unrealscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("unrealscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 99296, 23));
  }),
  uorazor: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("uorazor", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 7246, 23));
  }),
  uri: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("uri", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 12384, 23));
  }),
  v: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("v", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 37612, 23));
  }),
  vala: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vala", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 49314, 23));
  }),
  vbnet: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vbnet", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 76357, 23));
  }),
  velocity: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("velocity", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 52499, 23));
  }),
  verilog: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("verilog", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 20468, 23));
  }),
  vhdl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vhdl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 85558, 23));
  }),
  vim: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("vim", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 60470, 23));
  }),
  visualBasic: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("visualBasic", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 31675, 23));
  }),
  warpscript: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("warpscript", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 68549, 23));
  }),
  wasm: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("wasm", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 69592, 23));
  }),
  webIdl: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("webIdl", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 15250, 23));
  }),
  wiki: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("wiki", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 47940, 23));
  }),
  wolfram: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("wolfram", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 59058, 23));
  }),
  wren: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("wren", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 99210, 23));
  }),
  xeora: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("xeora", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 77767, 23));
  }),
  xmlDoc: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("xmlDoc", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 45232, 23));
  }),
  xojo: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("xojo", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 94254, 23));
  }),
  xquery: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("xquery", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 76036, 23));
  }),
  yaml: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("yaml", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 82171, 23));
  }),
  yang: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("yang", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 88029, 23));
  }),
  zig: (0,_create_language_async_loader__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)("zig", function () {
    return Promise.resolve(/* import() */).then(__webpack_require__.t.bind(__webpack_require__, 31944, 23));
  })
});

/***/ })

}]);