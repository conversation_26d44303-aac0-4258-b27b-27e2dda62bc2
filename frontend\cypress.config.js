const { defineConfig } = require('cypress');
const webpackConfig = require('./cypress/webpack.config.js');

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    supportFile: 'cypress/support/e2e.js',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 30000,
    pageLoadTimeout: 60000,

    setupNodeEvents(on, config) {
      // Webpack preprocessor for modern JS/JSX
      on('file:preprocessor', require('@cypress/webpack-preprocessor')({
        webpackOptions: webpackConfig,
        watchOptions: {}
      }));

      // Task for seeding test data
      on('task', {
        seedDatabase() {
          console.log('Seeding test database...');
          return null;
        },

        clearDatabase() {
          console.log('Clearing test database...');
          return null;
        },

        log(message) {
          console.log(message);
          return null;
        }
      });

      // Browser launch options for better testing
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.name === 'chrome') {
          launchOptions.args.push('--disable-dev-shm-usage');
          launchOptions.args.push('--no-sandbox');
          launchOptions.args.push('--disable-gpu');
        }
        return launchOptions;
      });

      return config;
    }
  },

  component: {
    devServer: {
      framework: 'create-react-app',
      bundler: 'webpack',
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.js'
  },

  env: {
    // Environment variables for testing
    API_BASE_URL: 'http://localhost:8000/api',
    wsUrl: 'ws://localhost:8000/ws',
    TEST_USER_EMAIL: '<EMAIL>',
    TEST_USER_PASSWORD: 'testpassword123'
  },

  retries: {
    runMode: 2,
    openMode: 0,
  },

  watchForFileChanges: false,

  // Folders
  fixturesFolder: 'cypress/fixtures',
  screenshotsFolder: 'cypress/screenshots',
  videosFolder: 'cypress/videos',
  downloadsFolder: 'cypress/downloads',

  // Experimental features
  experimentalStudio: true
});
