"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[7208],{

/***/ 10256:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35048);
/* harmony import */ var react_syntax_highlighter_dist_cjs_styles_prism__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(50695);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU.TabPane;

/**
 * Export Preview
 * 
 * Shows a preview of the generated code with syntax highlighting
 * and provides options to copy or download the code.
 */

var ExportPreview = function ExportPreview(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$format = _ref.format,
    format = _ref$format === void 0 ? 'react' : _ref$format,
    _ref$settings = _ref.settings,
    settings = _ref$settings === void 0 ? {} : _ref$settings,
    onExport = _ref.onExport,
    onDownload = _ref.onDownload,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    generatedCode = _useState2[0],
    setGeneratedCode = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('main'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    activeFile = _useState4[0],
    setActiveFile = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({}),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    files = _useState6[0],
    setFiles = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    generating = _useState8[0],
    setGenerating = _useState8[1];

  // Generate code when components or settings change
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (components.length > 0) {
      generateCode();
    }
  }, [components, format, settings]);
  var generateCode = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
      var mockCode;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setGenerating(true);
            try {
              // Mock code generation - in real app this would call the export service
              mockCode = generateMockCode(components, format, settings);
              setGeneratedCode(mockCode.main);
              setFiles(mockCode.files);
            } catch (error) {
              antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Failed to generate code');
            } finally {
              setGenerating(false);
            }
          case 1:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function generateCode() {
      return _ref2.apply(this, arguments);
    };
  }();
  var generateMockCode = function generateMockCode(components, format, settings) {
    var includeStyles = settings.includeStyles,
      typescript = settings.typescript,
      includeTests = settings.includeTests;
    var ext = typescript ? 'tsx' : 'jsx';
    var mainCode = '';
    var files = {};
    switch (format) {
      case 'react':
        mainCode = generateReactCode(components, settings);
        files = _objectSpread(_objectSpread((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({
          main: mainCode
        }, "App.".concat(ext), mainCode), includeStyles && {
          'App.css': generateCSSCode(components)
        }), includeTests && (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, "App.test.".concat(ext), generateTestCode(components)));
        break;
      case 'vue':
        mainCode = generateVueCode(components, settings);
        files = _objectSpread({
          main: mainCode,
          'App.vue': mainCode
        }, includeTests && {
          'App.spec.js': generateVueTestCode(components)
        });
        break;
      case 'html':
        mainCode = generateHTMLCode(components, settings);
        files = {
          main: mainCode,
          'index.html': mainCode,
          'styles.css': generateCSSCode(components),
          'script.js': generateJSCode(components)
        };
        break;
      default:
        mainCode = "// ".concat(format, " export coming soon!\n// Components: ").concat(components.length);
        files = {
          main: mainCode
        };
    }
    return {
      main: mainCode,
      files: files
    };
  };
  var generateReactCode = function generateReactCode(components, settings) {
    var typescript = settings.typescript;
    var imports = typescript ? "import React from 'react';\nimport './App.css';\n\n" : "import React from 'react';\nimport './App.css';\n\n";
    var componentCode = components.map(function (comp) {
      return "  <".concat(comp.type).concat(comp.props ? " ".concat(Object.entries(comp.props).map(function (_ref4) {
        var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref4, 2),
          key = _ref5[0],
          value = _ref5[1];
        return "".concat(key, "=\"").concat(value, "\"");
      }).join(' ')) : '', " />");
    }).join('\n');
    return "".concat(imports, "function App() {\n  return (\n    <div className=\"app\">\n").concat(componentCode, "\n    </div>\n  );\n}\n\nexport default App;");
  };
  var generateVueCode = function generateVueCode(components, settings) {
    var componentCode = components.map(function (comp) {
      return "    <".concat(comp.type).concat(comp.props ? " ".concat(Object.entries(comp.props).map(function (_ref6) {
        var _ref7 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref6, 2),
          key = _ref7[0],
          value = _ref7[1];
        return ":".concat(key, "=\"").concat(value, "\"");
      }).join(' ')) : '', " />");
    }).join('\n');
    return "<template>\n  <div class=\"app\">\n".concat(componentCode, "\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App',\n  data() {\n    return {\n      // Component data\n    };\n  }\n};\n</script>\n\n<style scoped>\n.app {\n  padding: 20px;\n}\n</style>");
  };
  var generateHTMLCode = function generateHTMLCode(components, settings) {
    var componentCode = components.map(function (comp) {
      var _comp$props;
      return "    <div class=\"".concat(comp.type.toLowerCase(), "\">").concat(((_comp$props = comp.props) === null || _comp$props === void 0 ? void 0 : _comp$props.title) || comp.type, "</div>");
    }).join('\n');
    return "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Generated App</title>\n    <link rel=\"stylesheet\" href=\"styles.css\">\n</head>\n<body>\n    <div class=\"app\">\n".concat(componentCode, "\n    </div>\n    <script src=\"script.js\"></script>\n</body>\n</html>");
  };
  var generateCSSCode = function generateCSSCode(components) {
    return ".app {\n  padding: 20px;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n".concat(components.map(function (comp) {
      return ".".concat(comp.type.toLowerCase(), " {\n  margin: 10px 0;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}");
    }).join('\n\n'));
  };
  var generateTestCode = function generateTestCode(components) {
    return "import { render, screen } from '@testing-library/react';\nimport App from './App';\n\ntest('renders app components', () => {\n  render(<App />);\n  \n  // Test that components are rendered\n".concat(components.map(function (comp) {
      return "  expect(screen.getByText('".concat(comp.type, "')).toBeInTheDocument();");
    }).join('\n'), "\n});");
  };
  var generateVueTestCode = function generateVueTestCode(components) {
    return "import { mount } from '@vue/test-utils';\nimport App from './App.vue';\n\ndescribe('App', () => {\n  it('renders components', () => {\n    const wrapper = mount(App);\n    expect(wrapper.exists()).toBe(true);\n  });\n});";
  };
  var generateJSCode = function generateJSCode(components) {
    return "// Generated JavaScript\ndocument.addEventListener('DOMContentLoaded', function() {\n  console.log('App loaded with ".concat(components.length, " components');\n});");
  };
  var handleCopy = /*#__PURE__*/function () {
    var _ref8 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2() {
      var _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 1;
            return navigator.clipboard.writeText(generatedCode);
          case 1:
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Code copied to clipboard');
            _context2.next = 3;
            break;
          case 2:
            _context2.prev = 2;
            _t = _context2["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Failed to copy code');
          case 3:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 2]]);
    }));
    return function handleCopy() {
      return _ref8.apply(this, arguments);
    };
  }();
  var handleDownload = /*#__PURE__*/function () {
    var _ref9 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee3() {
      var blob, url, a, _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.prev = 0;
            if (!onDownload) {
              _context3.next = 2;
              break;
            }
            _context3.next = 1;
            return onDownload(format, settings);
          case 1:
            _context3.next = 3;
            break;
          case 2:
            // Create and download file
            blob = new Blob([generatedCode], {
              type: 'text/plain'
            });
            url = URL.createObjectURL(blob);
            a = document.createElement('a');
            a.href = url;
            a.download = "app.".concat(format === 'react' ? 'jsx' : format === 'vue' ? 'vue' : 'html');
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          case 3:
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Code downloaded successfully');
            _context3.next = 5;
            break;
          case 4:
            _context3.prev = 4;
            _t2 = _context3["catch"](0);
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Failed to download code');
          case 5:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[0, 4]]);
    }));
    return function handleDownload() {
      return _ref9.apply(this, arguments);
    };
  }();
  var getLanguage = function getLanguage(format) {
    switch (format) {
      case 'react':
        return 'jsx';
      case 'vue':
        return 'vue';
      case 'html':
        return 'html';
      case 'angular':
        return 'typescript';
      default:
        return 'javascript';
    }
  };
  if (generating || loading) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        textAlign: 'center',
        padding: '40px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Spin */ .tK, {
      size: "large"
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        marginTop: 16
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, null, "Generating ", format, " code...")));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginBottom: 16,
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CodeOutlined */ .C$o, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "Generated ", format.toUpperCase(), " Code"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary"
  }, "(", components.length, " components)")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CopyOutlined */ .wq3, null),
    onClick: handleCopy
  }, "Copy"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownloadOutlined */ .jsW, null),
    onClick: handleDownload
  }, "Download"))), Object.keys(files).length > 1 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU, {
    activeKey: activeFile,
    onChange: setActiveFile
  }, Object.entries(files).map(function (_ref0) {
    var _ref1 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref0, 2),
      filename = _ref1[0],
      code = _ref1[1];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
      tab: filename,
      key: filename
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_7__/* .Prism */ .My, {
      language: getLanguage(format),
      style: react_syntax_highlighter_dist_cjs_styles_prism__WEBPACK_IMPORTED_MODULE_8__/* .tomorrow */ .uf,
      customStyle: {
        margin: 0,
        maxHeight: '400px',
        fontSize: '13px'
      }
    }, code)));
  })) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_7__/* .Prism */ .My, {
    language: getLanguage(format),
    style: react_syntax_highlighter_dist_cjs_styles_prism__WEBPACK_IMPORTED_MODULE_8__/* .tomorrow */ .uf,
    customStyle: {
      margin: 0,
      maxHeight: '400px',
      fontSize: '13px'
    }
  }, generatedCode)));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExportPreview);

/***/ }),

/***/ 17438:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(70572);
/* harmony import */ var _hooks_useTemplates__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(2643);
/* harmony import */ var _TemplateGallery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(19871);
/* harmony import */ var _TemplateEditor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(77844);


var _templateObject, _templateObject2;







var Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_3__/* .Tabs */ .tU.TabPane;

/**
 * Template Manager
 * 
 * Main template management component that provides template creation,
 * editing, saving, and loading functionality.
 */

var TemplateContainer = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  position: relative;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var CompactTemplate = styled_components__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border: 1px solid #91d5ff;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s;\n  \n  &:hover {\n    background: #e6f7ff;\n    border-color: #40a9ff;\n  }\n  \n  .template-icon {\n    color: #1890ff;\n    font-size: 16px;\n  }\n  \n  .template-text {\n    flex: 1;\n    font-size: 13px;\n    color: #262626;\n  }\n  \n  .template-count {\n    background: #1890ff;\n    color: white;\n    border-radius: 10px;\n    padding: 2px 6px;\n    font-size: 11px;\n    font-weight: 600;\n  }\n"])));
var TemplateManager = function TemplateManager(_ref) {
  var _ref$templates = _ref.templates,
    templates = _ref$templates === void 0 ? [] : _ref$templates,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading,
    onSave = _ref.onSave,
    onLoad = _ref.onLoad,
    onDelete = _ref.onDelete,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact,
    _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$selectedComponen = _ref.selectedComponent,
    selectedComponent = _ref$selectedComponen === void 0 ? null : _ref$selectedComponen;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    modalVisible = _useState2[0],
    setModalVisible = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('gallery'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    activeTab = _useState4[0],
    setActiveTab = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    saveModalVisible = _useState6[0],
    setSaveModalVisible = _useState6[1];

  // Templates hook
  var _useTemplates = (0,_hooks_useTemplates__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)({
      enabled: true,
      autoRefresh: true
    }),
    hookTemplates = _useTemplates.templates,
    hookLoading = _useTemplates.loading,
    saveAsTemplate = _useTemplates.saveAsTemplate,
    loadTemplate = _useTemplates.loadTemplate,
    deleteTemplate = _useTemplates.deleteTemplate,
    refreshTemplates = _useTemplates.refreshTemplates;

  // Use hook data if no props provided
  var activeTemplates = templates.length > 0 ? templates : hookTemplates;
  var isLoading = loading || hookLoading;
  var templateCount = activeTemplates.length;

  // Handle template operations
  var handleSave = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (templateData) {
    if (onSave) {
      onSave(templateData);
    } else {
      saveAsTemplate(templateData);
    }
    setSaveModalVisible(false);
  }, [onSave, saveAsTemplate]);
  var handleLoad = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (template) {
    if (onLoad) {
      onLoad(template);
    } else {
      loadTemplate(template);
    }
    setModalVisible(false);
  }, [onLoad, loadTemplate]);
  var handleDelete = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (template) {
    if (onDelete) {
      onDelete(template);
    } else {
      deleteTemplate(template.id);
    }
  }, [onDelete, deleteTemplate]);

  // Compact mode for header/toolbar
  if (compact) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TemplateContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(CompactTemplate, {
      onClick: function onClick() {
        return setModalVisible(true);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .AppstoreOutlined */ .rS9, {
      className: "template-icon"
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
      className: "template-text"
    }, "Templates"), templateCount > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", {
      className: "template-count"
    }, templateCount)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
      title: "Save as Template"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
      type: "text",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SaveOutlined */ .ylI, null),
      onClick: function onClick() {
        return setSaveModalVisible(true);
      },
      disabled: !components || components.length === 0
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Modal */ .aF, {
      title: "Template Manager",
      open: modalVisible,
      onCancel: function onCancel() {
        return setModalVisible(false);
      },
      footer: null,
      width: 800,
      style: {
        top: 20
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tabs */ .tU, {
      activeKey: activeTab,
      onChange: setActiveTab
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TabPane, {
      tab: "Gallery",
      key: "gallery"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TemplateGallery__WEBPACK_IMPORTED_MODULE_7__["default"], {
      templates: activeTemplates,
      loading: isLoading,
      onLoad: handleLoad,
      onDelete: handleDelete,
      showActions: true
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TabPane, {
      tab: "Create",
      key: "create"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TemplateEditor__WEBPACK_IMPORTED_MODULE_8__["default"], {
      components: components,
      onSave: handleSave,
      mode: "create"
    })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Modal */ .aF, {
      title: "Save as Template",
      open: saveModalVisible,
      onCancel: function onCancel() {
        return setSaveModalVisible(false);
      },
      footer: null,
      width: 600
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TemplateEditor__WEBPACK_IMPORTED_MODULE_8__["default"], {
      components: components,
      onSave: handleSave,
      onCancel: function onCancel() {
        return setSaveModalVisible(false);
      },
      mode: "save"
    })));
  }

  // Full mode for dedicated template management area
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .AppstoreOutlined */ .rS9, {
    style: {
      color: '#1890ff'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    strong: true
  }, "Templates"), templateCount > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
    count: templateCount,
    style: {
      backgroundColor: '#1890ff'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .PlusOutlined */ .bW0, null),
    onClick: function onClick() {
      return setSaveModalVisible(true);
    },
    disabled: !components || components.length === 0
  }, "Save Template"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .AppstoreOutlined */ .rS9, null),
    onClick: function onClick() {
      return setModalVisible(true);
    }
  }, "Browse"))), isLoading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
    loading: true
  }) : templateCount > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8, {
    grid: {
      gutter: 16,
      column: 2
    },
    dataSource: activeTemplates.slice(0, 4),
    renderItem: function renderItem(template) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
        size: "small",
        cover: template.thumbnail && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("img", {
          alt: template.name,
          src: template.thumbnail,
          style: {
            height: 120,
            objectFit: 'cover'
          }
        }),
        actions: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
          key: "load",
          title: "Load Template"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
          type: "text",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DownloadOutlined */ .jsW, null),
          onClick: function onClick() {
            return handleLoad(template);
          }
        })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
          key: "preview",
          title: "Preview"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
          type: "text",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .EyeOutlined */ .Om2, null),
          onClick: function onClick() {
            setModalVisible(true);
            setActiveTab('gallery');
          }
        })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
          key: "delete",
          title: "Delete"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
          type: "text",
          danger: true,
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DeleteOutlined */ .SUY, null),
          onClick: function onClick() {
            return handleDelete(template);
          }
        }))]
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp.Meta, {
        title: template.name,
        description: template.description
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          marginTop: 8
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: "secondary",
        style: {
          fontSize: 12
        }
      }, template.category), template.isPublic && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
        status: "success",
        text: "Public"
      })))));
    }
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '20px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .LayoutOutlined */ .hy2, {
    style: {
      fontSize: 48,
      color: '#d9d9d9'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    type: "secondary"
  }, "No templates available")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .PlusOutlined */ .bW0, null),
    onClick: function onClick() {
      return setSaveModalVisible(true);
    },
    disabled: !components || components.length === 0
  }, "Create Your First Template"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Modal */ .aF, {
    title: "Template Manager",
    open: modalVisible,
    onCancel: function onCancel() {
      return setModalVisible(false);
    },
    footer: null,
    width: 900,
    style: {
      top: 20
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TabPane, {
    tab: "Gallery",
    key: "gallery"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TemplateGallery__WEBPACK_IMPORTED_MODULE_7__["default"], {
    templates: activeTemplates,
    loading: isLoading,
    onLoad: handleLoad,
    onDelete: handleDelete,
    showActions: true
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(TabPane, {
    tab: "Create",
    key: "create"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TemplateEditor__WEBPACK_IMPORTED_MODULE_8__["default"], {
    components: components,
    onSave: handleSave,
    mode: "create"
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Modal */ .aF, {
    title: "Save as Template",
    open: saveModalVisible,
    onCancel: function onCancel() {
      return setSaveModalVisible(false);
    },
    footer: null,
    width: 600
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_TemplateEditor__WEBPACK_IMPORTED_MODULE_8__["default"], {
    components: components,
    onSave: handleSave,
    onCancel: function onCancel() {
      return setSaveModalVisible(false);
    },
    mode: "save"
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemplateManager);

/***/ }),

/***/ 19871:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(35346);





var Search = antd__WEBPACK_IMPORTED_MODULE_3__/* .Input */ .pd.Search;
var Option = antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6.Option;
var Text = antd__WEBPACK_IMPORTED_MODULE_3__/* .Typography */ .o5.Text;

/**
 * Template Gallery
 * 
 * Displays a gallery of available templates with search, filter, and preview functionality.
 */

var TemplateGallery = function TemplateGallery(_ref) {
  var _ref$templates = _ref.templates,
    templates = _ref$templates === void 0 ? [] : _ref$templates,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading,
    onLoad = _ref.onLoad,
    onDelete = _ref.onDelete,
    onPreview = _ref.onPreview,
    _ref$showActions = _ref.showActions,
    showActions = _ref$showActions === void 0 ? true : _ref$showActions,
    _ref$selectable = _ref.selectable,
    selectable = _ref$selectable === void 0 ? false : _ref$selectable,
    _ref$selectedTemplate = _ref.selectedTemplates,
    selectedTemplates = _ref$selectedTemplate === void 0 ? [] : _ref$selectedTemplate,
    onSelectionChange = _ref.onSelectionChange;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    searchTerm = _useState2[0],
    setSearchTerm = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    categoryFilter = _useState4[0],
    setCategoryFilter = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    typeFilter = _useState6[0],
    setTypeFilter = _useState6[1];

  // Get unique categories and types
  var categories = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    var cats = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(new Set(templates.map(function (t) {
      return t.category;
    }).filter(Boolean)));
    return cats.sort();
  }, [templates]);
  var types = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    var typeSet = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(new Set(templates.map(function (t) {
      return t.type || 'layout';
    }).filter(Boolean)));
    return typeSet.sort();
  }, [templates]);

  // Filter templates
  var filteredTemplates = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    return templates.filter(function (template) {
      var matchesSearch = !searchTerm || template.name.toLowerCase().includes(searchTerm.toLowerCase()) || template.description.toLowerCase().includes(searchTerm.toLowerCase());
      var matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
      var matchesType = typeFilter === 'all' || (template.type || 'layout') === typeFilter;
      return matchesSearch && matchesCategory && matchesType;
    });
  }, [templates, searchTerm, categoryFilter, typeFilter]);
  var getTypeIcon = function getTypeIcon(type) {
    switch (type) {
      case 'app':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .AppstoreOutlined */ .rS9, null);
      case 'mobile':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .MobileOutlined */ .jHj, null);
      default:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .LayoutOutlined */ .hy2, null);
    }
  };
  var getTypeColor = function getTypeColor(type) {
    switch (type) {
      case 'app':
        return '#722ed1';
      case 'mobile':
        return '#13c2c2';
      default:
        return '#1890ff';
    }
  };
  var renderTemplateCard = function renderTemplateCard(template) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp, {
      hoverable: true,
      cover: template.thumbnail ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("img", {
        alt: template.name,
        src: template.thumbnail,
        style: {
          height: 160,
          objectFit: 'cover'
        },
        onError: function onError(e) {
          e.target.style.display = 'none';
        }
      }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          height: 160,
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#8c8c8c'
        }
      }, getTypeIcon(template.type)),
      actions: showActions ? [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
        key: "load",
        title: "Load Template"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
        type: "text",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DownloadOutlined */ .jsW, null),
        onClick: function onClick() {
          return onLoad && onLoad(template);
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
        key: "preview",
        title: "Preview"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
        type: "text",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .EyeOutlined */ .Om2, null),
        onClick: function onClick() {
          return onPreview && onPreview(template);
        }
      }))].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(template.canDelete ? [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Tooltip */ .m_, {
        key: "delete",
        title: "Delete"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
        type: "text",
        danger: true,
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .DeleteOutlined */ .SUY, null),
        onClick: function onClick() {
          return onDelete && onDelete(template);
        }
      }))] : [])) : [],
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Card */ .Zp.Meta, {
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("span", null, template.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
        color: getTypeColor(template.type),
        text: template.type || 'layout',
        style: {
          fontSize: 11
        }
      })),
      description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: "secondary",
        style: {
          fontSize: 12
        }
      }, template.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
        style: {
          marginTop: 8
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
        size: 4
      }, template.category && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
        status: "default",
        text: template.category
      }), template.isPublic && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Badge */ .Ex, {
        status: "success",
        text: "Public"
      }), template.componentCount && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
        type: "secondary",
        style: {
          fontSize: 11
        }
      }, template.componentCount, " components"))))
    }));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Space */ .$x, {
    wrap: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Search, {
    placeholder: "Search templates...",
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    style: {
      width: 200
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .SearchOutlined */ .VrN, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: categoryFilter,
    onChange: setCategoryFilter,
    style: {
      width: 120
    },
    suffixIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .FilterOutlined */ .Lxx, null)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "all"
  }, "All Categories"), categories.map(function (category) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
      key: category,
      value: category
    }, category);
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Select */ .l6, {
    value: typeFilter,
    onChange: setTypeFilter,
    style: {
      width: 100
    },
    suffixIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__/* .FilterOutlined */ .Lxx, null)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
    value: "all"
  }, "All Types"), types.map(function (type) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Option, {
      key: type,
      value: type
    }, type);
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Text, {
    type: "secondary"
  }, filteredTemplates.length, " template", filteredTemplates.length !== 1 ? 's' : '', " found")), filteredTemplates.length > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8, {
    grid: {
      gutter: 16,
      xs: 1,
      sm: 2,
      md: 2,
      lg: 3,
      xl: 3,
      xxl: 4
    },
    dataSource: filteredTemplates,
    loading: loading,
    renderItem: function renderItem(template) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .List */ .B8.Item, null, renderTemplateCard(template));
    },
    pagination: {
      pageSize: 12,
      showSizeChanger: false,
      showQuickJumper: true,
      hideOnSinglePage: true
    }
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Empty */ .Sv, {
    image: antd__WEBPACK_IMPORTED_MODULE_3__/* .Empty */ .Sv.PRESENTED_IMAGE_SIMPLE,
    description: searchTerm || categoryFilter !== 'all' || typeFilter !== 'all' ? "No templates match your filters" : "No templates available",
    style: {
      padding: '40px 0'
    }
  }, (searchTerm || categoryFilter !== 'all' || typeFilter !== 'all') && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(antd__WEBPACK_IMPORTED_MODULE_3__/* .Button */ .$n, {
    type: "primary",
    onClick: function onClick() {
      setSearchTerm('');
      setCategoryFilter('all');
      setTypeFilter('all');
    }
  }, "Clear Filters")));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemplateGallery);

/***/ }),

/***/ 36243:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(35346);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



var Text = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_2__/* .Typography */ .o5.Title;
var Option = antd__WEBPACK_IMPORTED_MODULE_2__/* .Select */ .l6.Option;

/**
 * Export Settings
 * 
 * Configuration panel for code export options and preferences.
 */

var ExportSettings = function ExportSettings(_ref) {
  var _ref$settings = _ref.settings,
    settings = _ref$settings === void 0 ? {} : _ref$settings,
    onChange = _ref.onChange,
    _ref$format = _ref.format,
    format = _ref$format === void 0 ? 'react' : _ref$format;
  var handleSettingChange = function handleSettingChange(key, value) {
    onChange(_objectSpread(_objectSpread({}, settings), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, key, value)));
  };
  var getFormatSpecificSettings = function getFormatSpecificSettings() {
    switch (format) {
      case 'react':
      case 'react-native':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
          label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "TypeScript", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
            title: "Generate TypeScript code instead of JavaScript"
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
            style: {
              color: '#8c8c8c'
            }
          })))
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
          checked: settings.typescript,
          onChange: function onChange(checked) {
            return handleSettingChange('typescript', checked);
          }
        })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
          label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Functional Components", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
            title: "Use functional components instead of class components"
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
            style: {
              color: '#8c8c8c'
            }
          })))
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
          checked: settings.functionalComponents !== false,
          onChange: function onChange(checked) {
            return handleSettingChange('functionalComponents', checked);
          }
        })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
          label: "State Management"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Select */ .l6, {
          value: settings.stateManagement || 'useState',
          onChange: function onChange(value) {
            return handleSettingChange('stateManagement', value);
          },
          style: {
            width: '100%'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "useState"
        }, "React Hooks (useState)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "redux"
        }, "Redux"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "zustand"
        }, "Zustand"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "context"
        }, "Context API"))));
      case 'vue':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
          label: "Vue Version"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Select */ .l6, {
          value: settings.vueVersion || '3',
          onChange: function onChange(value) {
            return handleSettingChange('vueVersion', value);
          },
          style: {
            width: '100%'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "2"
        }, "Vue 2"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "3"
        }, "Vue 3"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
          label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Composition API", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
            title: "Use Composition API instead of Options API"
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
            style: {
              color: '#8c8c8c'
            }
          })))
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
          checked: settings.compositionAPI,
          onChange: function onChange(checked) {
            return handleSettingChange('compositionAPI', checked);
          },
          disabled: settings.vueVersion === '2'
        })));
      case 'angular':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
          label: "Angular Version"
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Select */ .l6, {
          value: settings.angularVersion || '15',
          onChange: function onChange(value) {
            return handleSettingChange('angularVersion', value);
          },
          style: {
            width: '100%'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "12"
        }, "Angular 12"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "13"
        }, "Angular 13"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "14"
        }, "Angular 14"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "15"
        }, "Angular 15"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
          value: "16"
        }, "Angular 16"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
          label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Standalone Components", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
            title: "Use standalone components (Angular 14+)"
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
            style: {
              color: '#8c8c8c'
            }
          })))
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
          checked: settings.standaloneComponents,
          onChange: function onChange(checked) {
            return handleSettingChange('standaloneComponents', checked);
          }
        })));
      default:
        return null;
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Title, {
    level: 4
  }, "Export Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV, {
    layout: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "General",
    size: "small",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Include Styles", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
      title: "Include CSS/styling files in the export"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: '#8c8c8c'
      }
    })))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
    checked: settings.includeStyles !== false,
    onChange: function onChange(checked) {
      return handleSettingChange('includeStyles', checked);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Include Tests", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
      title: "Generate test files for components"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: '#8c8c8c'
      }
    })))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
    checked: settings.includeTests,
    onChange: function onChange(checked) {
      return handleSettingChange('includeTests', checked);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Bundled Export", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
      title: "Export as a single file instead of multiple files"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: '#8c8c8c'
      }
    })))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
    checked: settings.bundled,
    onChange: function onChange(checked) {
      return handleSettingChange('bundled', checked);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: "Code Style"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Select */ .l6, {
    value: settings.codeStyle || 'prettier',
    onChange: function onChange(value) {
      return handleSettingChange('codeStyle', value);
    },
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "prettier"
  }, "Prettier"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "eslint"
  }, "ESLint"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "standard"
  }, "Standard"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "none"
  }, "None")))), getFormatSpecificSettings() && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "".concat(format.charAt(0).toUpperCase() + format.slice(1), " Settings"),
    size: "small",
    style: {
      marginBottom: 16
    }
  }, getFormatSpecificSettings()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "Styling",
    size: "small",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: "CSS Framework"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Select */ .l6, {
    value: settings.cssFramework || 'none',
    onChange: function onChange(value) {
      return handleSettingChange('cssFramework', value);
    },
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "none"
  }, "None"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "tailwind"
  }, "Tailwind CSS"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "bootstrap"
  }, "Bootstrap"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "antd"
  }, "Ant Design"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "material-ui"
  }, "Material-UI"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "chakra"
  }, "Chakra UI"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: "CSS Preprocessor"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Select */ .l6, {
    value: settings.cssPreprocessor || 'css',
    onChange: function onChange(value) {
      return handleSettingChange('cssPreprocessor', value);
    },
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "css"
  }, "CSS"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "scss"
  }, "SCSS"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "sass"
  }, "Sass"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "less"
  }, "Less"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "styled-components"
  }, "Styled Components"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Responsive Design", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
      title: "Include responsive breakpoints and mobile-first design"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: '#8c8c8c'
      }
    })))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
    checked: settings.responsive !== false,
    onChange: function onChange(checked) {
      return handleSettingChange('responsive', checked);
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Card */ .Zp, {
    title: "Build Configuration",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: "Build Tool"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Select */ .l6, {
    value: settings.buildTool || 'webpack',
    onChange: function onChange(value) {
      return handleSettingChange('buildTool', value);
    },
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "webpack"
  }, "Webpack"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "vite"
  }, "Vite"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "parcel"
  }, "Parcel"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "rollup"
  }, "Rollup"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(Option, {
    value: "create-react-app"
  }, "Create React App"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Include Package.json", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
      title: "Include package.json with dependencies"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: '#8c8c8c'
      }
    })))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
    checked: settings.includePackageJson !== false,
    onChange: function onChange(checked) {
      return handleSettingChange('includePackageJson', checked);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Form */ .lV.Item, {
    label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Space */ .$x, null, "Include README", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Tooltip */ .m_, {
      title: "Generate README.md with setup instructions"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_3__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: '#8c8c8c'
      }
    })))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .Switch */ .dO, {
    checked: settings.includeReadme,
    onChange: function onChange(checked) {
      return handleSettingChange('includeReadme', checked);
    }
  })))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExportSettings);

/***/ }),

/***/ 48796:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);
/* harmony import */ var _hooks_useCodeExport__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(66337);
/* harmony import */ var _ExportPreview__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(10256);
/* harmony import */ var _ExportSettings__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(36243);



var _templateObject, _templateObject2;








var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU.TabPane;

/**
 * Code Exporter
 * 
 * Main code export component that provides code generation and download
 * functionality for multiple frameworks and formats.
 */

var ExportContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n"])));
var CompactExport = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: #fff7e6;\n  border: 1px solid #ffd591;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s;\n  \n  &:hover {\n    background: #fff1b8;\n    border-color: #ffec3d;\n  }\n  \n  .export-icon {\n    color: #fa8c16;\n    font-size: 16px;\n  }\n  \n  .export-text {\n    flex: 1;\n    font-size: 13px;\n    color: #262626;\n  }\n  \n  .export-count {\n    background: #fa8c16;\n    color: white;\n    border-radius: 10px;\n    padding: 2px 6px;\n    font-size: 11px;\n    font-weight: 600;\n  }\n"])));
var CodeExporter = function CodeExporter(_ref) {
  var _ref$formats = _ref.formats,
    formats = _ref$formats === void 0 ? [] : _ref$formats,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading,
    onExport = _ref.onExport,
    onDownload = _ref.onDownload,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact,
    _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$selectedFormat = _ref.selectedFormat,
    selectedFormat = _ref$selectedFormat === void 0 ? 'react' : _ref$selectedFormat;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    modalVisible = _useState2[0],
    setModalVisible = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('export'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    activeTab = _useState4[0],
    setActiveTab = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(selectedFormat),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    currentFormat = _useState6[0],
    setCurrentFormat = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      includeStyles: true,
      includeTests: false,
      typescript: false,
      bundled: false
    }),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    exportSettings = _useState8[0],
    setExportSettings = _useState8[1];

  // Code export hook
  var _useCodeExport = (0,_hooks_useCodeExport__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)({
      enabled: true,
      components: components,
      settings: exportSettings
    }),
    hookFormats = _useCodeExport.exportFormats,
    hookLoading = _useCodeExport.loading,
    exportCode = _useCodeExport.exportCode,
    downloadCode = _useCodeExport.downloadCode,
    previewCode = _useCodeExport.previewCode,
    getAvailableFormats = _useCodeExport.getAvailableFormats;

  // Use hook data if no props provided
  var activeFormats = formats.length > 0 ? formats : hookFormats;
  var isLoading = loading || hookLoading;
  var formatCount = activeFormats.length;

  // Handle export operations
  var handleExport = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(format, settings) {
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!onExport) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return", onExport(format, settings));
          case 1:
            return _context.abrupt("return", exportCode(format, settings));
          case 2:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x, _x2) {
      return _ref2.apply(this, arguments);
    };
  }(), [onExport, exportCode]);
  var handleDownload = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(format, settings) {
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            if (!onDownload) {
              _context2.next = 1;
              break;
            }
            return _context2.abrupt("return", onDownload(format, settings));
          case 1:
            return _context2.abrupt("return", downloadCode(format, settings));
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function (_x3, _x4) {
      return _ref3.apply(this, arguments);
    };
  }(), [onDownload, downloadCode]);
  var handleQuickExport = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee3() {
    var _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 1;
          return handleDownload(currentFormat, exportSettings);
        case 1:
          _context3.next = 3;
          break;
        case 2:
          _context3.prev = 2;
          _t = _context3["catch"](0);
          console.error('Export failed:', _t);
        case 3:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 2]]);
  })), [currentFormat, exportSettings, handleDownload]);

  // Available export formats
  var defaultFormats = [{
    value: 'react',
    label: 'React',
    icon: '⚛️'
  }, {
    value: 'vue',
    label: 'Vue.js',
    icon: '🟢'
  }, {
    value: 'angular',
    label: 'Angular',
    icon: '🔴'
  }, {
    value: 'svelte',
    label: 'Svelte',
    icon: '🧡'
  }, {
    value: 'html',
    label: 'HTML/CSS',
    icon: '📄'
  }, {
    value: 'react-native',
    label: 'React Native',
    icon: '📱'
  }, {
    value: 'flutter',
    label: 'Flutter',
    icon: '💙'
  }];
  var availableFormats = activeFormats.length > 0 ? activeFormats : defaultFormats;

  // Compact mode for header/toolbar
  if (compact) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ExportContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(CompactExport, {
      onClick: function onClick() {
        return setModalVisible(true);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CodeOutlined */ .C$o, {
      className: "export-icon"
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
      className: "export-text"
    }, "Export Code"), formatCount > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
      className: "export-count"
    }, formatCount)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
      value: currentFormat,
      onChange: setCurrentFormat,
      style: {
        width: 100
      },
      size: "small"
    }, availableFormats.map(function (format) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
        key: format.value,
        value: format.value
      }, format.icon, " ", format.label);
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
      title: "Quick Export"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      type: "text",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownloadOutlined */ .jsW, null),
      onClick: handleQuickExport,
      loading: isLoading,
      disabled: !components || components.length === 0
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Modal */ .aF, {
      title: "Code Exporter",
      open: modalVisible,
      onCancel: function onCancel() {
        return setModalVisible(false);
      },
      footer: null,
      width: 900,
      style: {
        top: 20
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU, {
      activeKey: activeTab,
      onChange: setActiveTab
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
      tab: "Export",
      key: "export"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ExportPreview__WEBPACK_IMPORTED_MODULE_9__["default"], {
      components: components,
      format: currentFormat,
      settings: exportSettings,
      onExport: handleExport,
      onDownload: handleDownload,
      loading: isLoading
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
      tab: "Settings",
      key: "settings"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ExportSettings__WEBPACK_IMPORTED_MODULE_10__["default"], {
      settings: exportSettings,
      onChange: setExportSettings,
      format: currentFormat
    })))));
  }

  // Full mode for dedicated export area
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CodeOutlined */ .C$o, {
    style: {
      color: '#fa8c16'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "Code Export"), formatCount > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
    count: formatCount,
    style: {
      backgroundColor: '#fa8c16'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: currentFormat,
    onChange: setCurrentFormat,
    style: {
      width: 120
    }
  }, availableFormats.map(function (format) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
      key: format.value,
      value: format.value
    }, format.icon, " ", format.label);
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownloadOutlined */ .jsW, null),
    onClick: handleQuickExport,
    loading: isLoading,
    disabled: !components || components.length === 0
  }, "Export"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, null),
    onClick: function onClick() {
      return setModalVisible(true);
    }
  }, "Advanced"))), isLoading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '20px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, null, "Generating code...")))) : components && components.length > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "Ready to export:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary",
    style: {
      marginLeft: 8
    }
  }, components.length, " component", components.length !== 1 ? 's' : '')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: 12
    }
  }, availableFormats.slice(0, 4).map(function (format) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
      key: format.value,
      style: {
        height: 'auto',
        padding: '12px'
      },
      onClick: function onClick() {
        setCurrentFormat(format.value);
        handleQuickExport();
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        textAlign: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        fontSize: 20,
        marginBottom: 4
      }
    }, format.icon), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, format.label)));
  })))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '20px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FileTextOutlined */ .y9H, {
    style: {
      fontSize: 48,
      color: '#d9d9d9'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary"
  }, "Add components to enable code export"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Modal */ .aF, {
    title: "Code Exporter",
    open: modalVisible,
    onCancel: function onCancel() {
      return setModalVisible(false);
    },
    footer: null,
    width: 1000,
    style: {
      top: 20
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Export",
    key: "export"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ExportPreview__WEBPACK_IMPORTED_MODULE_9__["default"], {
    components: components,
    format: currentFormat,
    settings: exportSettings,
    onExport: handleExport,
    onDownload: handleDownload,
    loading: isLoading
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Settings",
    key: "settings"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ExportSettings__WEBPACK_IMPORTED_MODULE_10__["default"], {
    settings: exportSettings,
    onChange: setExportSettings,
    format: currentFormat
  })))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CodeExporter);

/***/ }),

/***/ 77844:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



var TextArea = antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.TextArea;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text;

/**
 * Template Editor
 * 
 * Form component for creating and editing templates with metadata and preview.
 */

var TemplateEditor = function TemplateEditor(_ref) {
  var _ref$template = _ref.template,
    template = _ref$template === void 0 ? null : _ref$template,
    _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onSave = _ref.onSave,
    onCancel = _ref.onCancel,
    _ref$mode = _ref.mode,
    mode = _ref$mode === void 0 ? 'create' : _ref$mode;
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setSaving = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)((template === null || template === void 0 ? void 0 : template.thumbnail) || ''),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    thumbnailUrl = _useState4[0],
    setThumbnailUrl = _useState4[1];
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (template) {
      form.setFieldsValue({
        name: template.name,
        description: template.description,
        category: template.category,
        type: template.type || 'layout',
        isPublic: template.isPublic || false,
        tags: template.tags || []
      });
      setThumbnailUrl(template.thumbnail || '');
    } else if (mode === 'save') {
      // Auto-generate name for save mode
      var timestamp = new Date().toLocaleDateString();
      form.setFieldsValue({
        name: "My Template - ".concat(timestamp),
        description: 'Template created from current app design',
        category: 'custom',
        type: 'layout',
        isPublic: false,
        tags: []
      });
    }
  }, [template, mode, form]);
  var handleSave = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(values) {
      var templateData, _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setSaving(true);
            _context.prev = 1;
            templateData = _objectSpread(_objectSpread({}, values), {}, {
              components: components || [],
              componentCount: (components === null || components === void 0 ? void 0 : components.length) || 0,
              thumbnail: thumbnailUrl,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            });
            if (template) {
              templateData.id = template.id;
            }
            _context.next = 2;
            return onSave(templateData);
          case 2:
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success("Template ".concat(mode === 'edit' ? 'updated' : 'saved', " successfully!"));
            form.resetFields();
            _context.next = 4;
            break;
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error("Failed to ".concat(mode === 'edit' ? 'update' : 'save', " template"));
          case 4:
            _context.prev = 4;
            setSaving(false);
            return _context.finish(4);
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3, 4, 5]]);
    }));
    return function handleSave(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleThumbnailUpload = function handleThumbnailUpload(info) {
    if (info.file.status === 'done') {
      setThumbnailUrl(info.file.response.url);
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Thumbnail uploaded successfully');
    } else if (info.file.status === 'error') {
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Thumbnail upload failed');
    }
  };
  var categories = ['basic', 'business', 'portfolio', 'blog', 'ecommerce', 'dashboard', 'landing', 'custom'];
  var types = [{
    value: 'layout',
    label: 'Layout Template'
  }, {
    value: 'app',
    label: 'App Template'
  }, {
    value: 'component',
    label: 'Component Template'
  }, {
    value: 'mobile',
    label: 'Mobile Template'
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV, {
    form: form,
    layout: "vertical",
    onFinish: handleSave,
    initialValues: {
      type: 'layout',
      isPublic: false,
      category: 'custom'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "name",
    label: "Template Name",
    rules: [{
      required: true,
      message: 'Please enter a template name'
    }, {
      min: 3,
      message: 'Name must be at least 3 characters'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    placeholder: "Enter template name"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "description",
    label: "Description",
    rules: [{
      required: true,
      message: 'Please enter a description'
    }, {
      min: 10,
      message: 'Description must be at least 10 characters'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TextArea, {
    rows: 3,
    placeholder: "Describe what this template is for and what it includes"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    style: {
      width: '100%'
    },
    size: "large"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "category",
    label: "Category",
    rules: [{
      required: true,
      message: 'Please select a category'
    }],
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    placeholder: "Select category"
  }, categories.map(function (category) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
      key: category,
      value: category
    }, category.charAt(0).toUpperCase() + category.slice(1));
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "type",
    label: "Type",
    rules: [{
      required: true,
      message: 'Please select a type'
    }],
    style: {
      flex: 1
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    placeholder: "Select type"
  }, types.map(function (type) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
      key: type.value,
      value: type.value
    }, type.label);
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "tags",
    label: "Tags",
    help: "Add tags to help others find your template"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    mode: "tags",
    placeholder: "Add tags (press Enter to add)",
    style: {
      width: '100%'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    name: "isPublic",
    label: "Visibility",
    valuePropName: "checked"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checkedChildren: "Public",
    unCheckedChildren: "Private"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    label: "Thumbnail"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Upload */ ._O, {
    name: "thumbnail",
    listType: "picture-card",
    className: "template-thumbnail-uploader",
    showUploadList: false,
    action: "/api/upload/thumbnail",
    onChange: handleThumbnailUpload
  }, thumbnailUrl ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("img", {
    src: thumbnailUrl,
    alt: "thumbnail",
    style: {
      width: '100%',
      height: '100%',
      objectFit: 'cover'
    }
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UploadOutlined */ .qvO, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, "Upload"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    type: "secondary",
    style: {
      fontSize: 12
    }
  }, "Recommended size: 400x300px")), components && components.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp, {
    title: "Template Preview",
    size: "small",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginBottom: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, "Components included: "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, null, components.length)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      maxHeight: 120,
      overflowY: 'auto'
    }
  }, components.map(function (component, index) {
    var _component$props;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      key: index,
      style: {
        fontSize: 12,
        color: '#666'
      }
    }, "\u2022 ", component.type, " ", ((_component$props = component.props) === null || _component$props === void 0 ? void 0 : _component$props.title) && "- ".concat(component.props.title));
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, {
    style: {
      marginBottom: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    htmlType: "submit",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SaveOutlined */ .ylI, null),
    loading: loading
  }, mode === 'edit' ? 'Update Template' : 'Save Template'), onCancel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    onClick: onCancel
  }, "Cancel"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "dashed",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EyeOutlined */ .Om2, null),
    onClick: function onClick() {
      // Preview functionality could be added here
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.info('Preview functionality coming soon');
    }
  }, "Preview")))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemplateEditor);

/***/ })

}]);