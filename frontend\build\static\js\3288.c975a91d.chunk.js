"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3288],{

/***/ 3288:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ services_aiDesignService)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
;// ./src/config/aiConfig.js


/**
 * AI Configuration
 * Centralized configuration for AI features and services
 */

// Environment variables
var API_URL = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_28288_YAYINGOBOMPBXKSI","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_13760_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_API_URL || 'http://localhost:8000';
var AI_ENABLED = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_28288_YAYINGOBOMPBXKSI","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_13760_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_ENABLED !== 'false'; // Default to true
var AI_FALLBACK_ENABLED = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_28288_YAYINGOBOMPBXKSI","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_13760_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_FALLBACK_ENABLED !== 'false'; // Default to true

// AI Service Configuration
var aiConfig = {
  // Core settings
  enabled: AI_ENABLED,
  fallbackEnabled: AI_FALLBACK_ENABLED,
  // API settings
  baseUrl: "".concat(API_URL, "/api/ai"),
  timeout: 10000,
  // 10 seconds
  retryAttempts: 2,
  retryDelay: 1000,
  // 1 second

  // Cache settings
  cacheEnabled: true,
  cacheTimeout: 5 * 60 * 1000,
  // 5 minutes

  // WebSocket settings
  websocketEnabled: true,
  websocketTimeout: 10000,
  // 10 seconds

  // Feature flags
  features: {
    layoutSuggestions: true,
    componentCombinations: true,
    appAnalysis: true,
    realTimeUpdates: true,
    collaborativeAI: false // Experimental
  },
  // Fallback behavior
  fallback: {
    showWarnings: false,
    // Set to false to reduce console noise
    useBasicAnalysis: true,
    provideFallbackSuggestions: true,
    gracefulDegradation: true
  },
  // Performance settings
  performance: {
    debounceDelay: 500,
    // Debounce AI requests
    maxConcurrentRequests: 3,
    backgroundRefresh: true,
    lazyLoading: true
  }
};

// Helper functions
var isAIEnabled = function isAIEnabled() {
  return aiConfig.enabled;
};
var isFeatureEnabled = function isFeatureEnabled(feature) {
  return aiConfig.enabled && aiConfig.features[feature];
};
var shouldShowWarnings = function shouldShowWarnings() {
  return aiConfig.fallback.showWarnings;
};

// Service availability checker
var checkAIServiceAvailability = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {
    var controller, timeoutId, response, _t;
    return _regeneratorRuntime.wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (aiConfig.enabled) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return", {
            available: false,
            reason: 'AI features disabled'
          });
        case 1:
          _context.prev = 1;
          controller = new AbortController();
          timeoutId = setTimeout(function () {
            return controller.abort();
          }, aiConfig.timeout);
          _context.next = 2;
          return fetch("".concat(aiConfig.baseUrl, "/health/"), {
            method: 'GET',
            signal: controller.signal,
            headers: {
              'Content-Type': 'application/json'
            }
          });
        case 2:
          response = _context.sent;
          clearTimeout(timeoutId);
          if (!response.ok) {
            _context.next = 3;
            break;
          }
          return _context.abrupt("return", {
            available: true,
            status: response.status
          });
        case 3:
          return _context.abrupt("return", {
            available: false,
            reason: "HTTP ".concat(response.status)
          });
        case 4:
          _context.next = 7;
          break;
        case 5:
          _context.prev = 5;
          _t = _context["catch"](1);
          if (!(_t.name === 'AbortError')) {
            _context.next = 6;
            break;
          }
          return _context.abrupt("return", {
            available: false,
            reason: 'Timeout'
          });
        case 6:
          return _context.abrupt("return", {
            available: false,
            reason: _t.message
          });
        case 7:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 5]]);
  }));
  return function checkAIServiceAvailability() {
    return _ref.apply(this, arguments);
  };
}()));

// Error handling configuration
var errorConfig = {
  // Log levels: 'error', 'warn', 'info', 'debug'
  logLevel:  false ? 0 : 'warn',
  // Error reporting
  reportErrors: false,
  // Set to true to enable error reporting

  // User-facing messages
  messages: {
    serviceUnavailable: 'AI suggestions are temporarily unavailable. Using basic recommendations.',
    networkError: 'Unable to connect to AI service. Check your internet connection.',
    timeout: 'AI service is taking too long to respond. Using cached results.',
    fallback: 'Using offline AI suggestions.'
  }
};

// Development helpers
var devConfig = {
  mockResponses: "production" === 'development',
  debugMode: {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_28288_YAYINGOBOMPBXKSI","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_13760_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_DEBUG === 'true',
  verboseLogging: {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_28288_YAYINGOBOMPBXKSI","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_13760_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_VERBOSE === 'true'
};
/* harmony default export */ const config_aiConfig = ((/* unused pure expression or super */ null && (aiConfig)));
;// ./src/services/aiWebSocketService.js


/**
 * AI WebSocket Service
 * Handles real-time AI suggestions via WebSocket connection
 */
var AIWebSocketService = /*#__PURE__*/function () {
  function AIWebSocketService() {
    (0,classCallCheck/* default */.A)(this, AIWebSocketService);
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.listeners = new Map();
    this.messageQueue = [];
    this.subscriptions = new Set();

    // Get WebSocket URL
    var protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    var host = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_28288_YAYINGOBOMPBXKSI","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_13760_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_WS_HOST || 'localhost:8000';
    this.wsUrl = "".concat(protocol, "//").concat(host, "/ws/ai-suggestions/");
  }

  /**
   * Connect to AI suggestions WebSocket
   */
  return (0,createClass/* default */.A)(AIWebSocketService, [{
    key: "connect",
    value: function connect() {
      var _this = this;
      if (this.isConnected || this.ws) {
        return Promise.resolve();
      }
      return new Promise(function (resolve, reject) {
        try {
          _this.ws = new WebSocket(_this.wsUrl);
          _this.ws.onopen = function () {
            console.log('AI WebSocket connected');
            _this.isConnected = true;
            _this.reconnectAttempts = 0;

            // Process queued messages
            _this.processMessageQueue();

            // Emit connection event
            _this.emit('connected');
            resolve();
          };
          _this.ws.onmessage = function (event) {
            try {
              var data = JSON.parse(event.data);
              _this.handleMessage(data);
            } catch (error) {
              console.error('Error parsing AI WebSocket message:', error);
            }
          };
          _this.ws.onclose = function (event) {
            console.log('AI WebSocket disconnected:', event.code, event.reason);
            _this.isConnected = false;
            _this.ws = null;

            // Emit disconnection event
            _this.emit('disconnected', {
              code: event.code,
              reason: event.reason
            });

            // Attempt reconnection if not intentional
            if (event.code !== 1000 && _this.reconnectAttempts < _this.maxReconnectAttempts) {
              _this.scheduleReconnect();
            }
          };
          _this.ws.onerror = function (error) {
            console.error('AI WebSocket error:', error);
            _this.emit('error', error);
            reject(error);
          };
        } catch (error) {
          console.error('Error creating AI WebSocket:', error);
          reject(error);
        }
      });
    }

    /**
     * Disconnect from WebSocket
     */
  }, {
    key: "disconnect",
    value: function disconnect() {
      if (this.ws) {
        this.ws.close(1000, 'Client disconnect');
        this.ws = null;
        this.isConnected = false;
      }
    }

    /**
     * Schedule reconnection attempt
     */
  }, {
    key: "scheduleReconnect",
    value: function scheduleReconnect() {
      var _this2 = this;
      this.reconnectAttempts++;
      var delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
      console.log("Scheduling AI WebSocket reconnect attempt ".concat(this.reconnectAttempts, " in ").concat(delay, "ms"));
      setTimeout(function () {
        if (!_this2.isConnected) {
          _this2.connect()["catch"](function (error) {
            console.error('AI WebSocket reconnect failed:', error);
          });
        }
      }, delay);
    }

    /**
     * Send message to WebSocket
     */
  }, {
    key: "send",
    value: function send(message) {
      if (this.isConnected && this.ws) {
        this.ws.send(JSON.stringify(message));
      } else {
        // Queue message for later
        this.messageQueue.push(message);

        // Try to connect if not connected
        if (!this.isConnected) {
          this.connect();
        }
      }
    }

    /**
     * Process queued messages
     */
  }, {
    key: "processMessageQueue",
    value: function processMessageQueue() {
      while (this.messageQueue.length > 0) {
        var message = this.messageQueue.shift();
        this.send(message);
      }
    }

    /**
     * Handle incoming WebSocket messages
     */
  }, {
    key: "handleMessage",
    value: function handleMessage(data) {
      var type = data.type;
      switch (type) {
        case 'connection_established':
          console.log('AI WebSocket connection established');
          break;
        case 'layout_suggestions':
          this.emit('layoutSuggestions', data.suggestions);
          break;
        case 'component_combinations':
          this.emit('componentCombinations', data.suggestions);
          break;
        case 'app_analysis':
          this.emit('appAnalysis', data.analysis);
          break;
        case 'layout_suggestions_broadcast':
          this.emit('layoutSuggestionsBroadcast', data.suggestions);
          break;
        case 'component_combinations_broadcast':
          this.emit('componentCombinationsBroadcast', data.suggestions);
          break;
        case 'ai_suggestion_update':
          this.emit('aiSuggestionUpdate', data);
          break;
        case 'error':
          console.error('AI WebSocket error:', data.message);
          this.emit('error', new Error(data.message));
          break;
        case 'pong':
          this.emit('pong', data);
          break;
        default:
          console.log('Unknown AI WebSocket message type:', type, data);
      }
    }

    /**
     * Request layout suggestions
     */
  }, {
    key: "requestLayoutSuggestions",
    value: function requestLayoutSuggestions(components) {
      var layouts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
      var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var broadcast = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
      this.send({
        type: 'get_layout_suggestions',
        components: components,
        layouts: layouts,
        context: context,
        broadcast: broadcast,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Request component combinations
     */
  }, {
    key: "requestComponentCombinations",
    value: function requestComponentCombinations(components) {
      var selectedComponent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var broadcast = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
      this.send({
        type: 'get_component_combinations',
        components: components,
        selected_component: selectedComponent,
        context: context,
        broadcast: broadcast,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Request app structure analysis
     */
  }, {
    key: "requestAppAnalysis",
    value: function requestAppAnalysis(components) {
      var layouts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
      this.send({
        type: 'analyze_app_structure',
        components: components,
        layouts: layouts,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Subscribe to AI updates
     */
  }, {
    key: "subscribeToUpdates",
    value: function subscribeToUpdates() {
      var subscriptionType = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'all';
      this.subscriptions.add(subscriptionType);
      this.send({
        type: 'subscribe_to_updates',
        subscription_type: subscriptionType,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Send ping to keep connection alive
     */
  }, {
    key: "ping",
    value: function ping() {
      this.send({
        type: 'ping',
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Add event listener
     */
  }, {
    key: "addEventListener",
    value: function addEventListener(event, callback) {
      if (!this.listeners.has(event)) {
        this.listeners.set(event, new Set());
      }
      this.listeners.get(event).add(callback);
    }

    /**
     * Remove event listener
     */
  }, {
    key: "removeEventListener",
    value: function removeEventListener(event, callback) {
      if (this.listeners.has(event)) {
        this.listeners.get(event)["delete"](callback);
      }
    }

    /**
     * Emit event to listeners
     */
  }, {
    key: "emit",
    value: function emit(event, data) {
      if (this.listeners.has(event)) {
        this.listeners.get(event).forEach(function (callback) {
          try {
            callback(data);
          } catch (error) {
            console.error("Error in AI WebSocket event listener for ".concat(event, ":"), error);
          }
        });
      }
    }

    /**
     * Get connection status
     */
  }, {
    key: "getStatus",
    value: function getStatus() {
      return {
        connected: this.isConnected,
        reconnectAttempts: this.reconnectAttempts,
        subscriptions: Array.from(this.subscriptions),
        queuedMessages: this.messageQueue.length
      };
    }
  }]);
}(); // Create singleton instance
var aiWebSocketService = new AIWebSocketService();

// Auto-connect when service is imported
if (typeof window !== 'undefined') {
  // Connect after a short delay to allow app initialization
  setTimeout(function () {
    aiWebSocketService.connect()["catch"](function (error) {
      console.warn('Initial AI WebSocket connection failed:', error);
    });
  }, 1000);
}
/* harmony default export */ const services_aiWebSocketService = (aiWebSocketService);
;// ./src/services/aiDesignService.js




/**
 * AI Design Service
 * Enhanced AI service for layout suggestions and component combinations
 */

// Import configuration


// Import WebSocket service

var AIDesignService = /*#__PURE__*/function () {
  function AIDesignService() {
    (0,classCallCheck/* default */.A)(this, AIDesignService);
    this.baseUrl = aiConfig.baseUrl;
    this.cache = new Map();
    this.cacheTimeout = aiConfig.cacheTimeout;
    this.useWebSocket = aiConfig.websocketEnabled;
    this.wsService = services_aiWebSocketService;
    this.enabled = isAIEnabled();

    // Setup WebSocket event listeners
    this.setupWebSocketListeners();
  }

  /**
   * Setup WebSocket event listeners
   */
  return (0,createClass/* default */.A)(AIDesignService, [{
    key: "setupWebSocketListeners",
    value: function setupWebSocketListeners() {
      var _this = this;
      this.wsService.addEventListener('layoutSuggestions', function (suggestions) {
        // Cache WebSocket results
        var cacheKey = 'ws_layout_suggestions';
        _this.cache.set(cacheKey, {
          data: {
            suggestions: suggestions,
            status: 'success'
          },
          timestamp: Date.now()
        });
      });
      this.wsService.addEventListener('componentCombinations', function (suggestions) {
        // Cache WebSocket results
        var cacheKey = 'ws_component_combinations';
        _this.cache.set(cacheKey, {
          data: {
            suggestions: suggestions,
            status: 'success'
          },
          timestamp: Date.now()
        });
      });
      this.wsService.addEventListener('error', function (error) {
        if (shouldShowWarnings()) {
          console.warn('AI WebSocket error, falling back to HTTP:', error);
        }
      });
    }

    /**
     * Generate layout suggestions based on app structure
     * @param {Array} components - Current components in the app
     * @param {Array} layouts - Existing layouts (optional)
     * @param {Object} context - Additional context (optional)
     * @returns {Promise<Object>} Layout suggestions response
     */
  }, {
    key: "generateLayoutSuggestions",
    value: (function () {
      var _generateLayoutSuggestions = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(components) {
        var layouts,
          context,
          cacheKey,
          cached,
          response,
          data,
          _args = arguments,
          _t,
          _t2;
        return regenerator_default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              layouts = _args.length > 1 && _args[1] !== undefined ? _args[1] : [];
              context = _args.length > 2 && _args[2] !== undefined ? _args[2] : {};
              if (this.enabled) {
                _context.next = 1;
                break;
              }
              return _context.abrupt("return", this._getFallbackLayoutSuggestions(components));
            case 1:
              cacheKey = "layout_".concat(JSON.stringify({
                components: components,
                layouts: layouts,
                context: context
              })); // Check cache first
              if (!this.cache.has(cacheKey)) {
                _context.next = 2;
                break;
              }
              cached = this.cache.get(cacheKey);
              if (!(Date.now() - cached.timestamp < this.cacheTimeout)) {
                _context.next = 2;
                break;
              }
              return _context.abrupt("return", cached.data);
            case 2:
              if (!(this.useWebSocket && this.wsService.getStatus().connected)) {
                _context.next = 6;
                break;
              }
              _context.prev = 3;
              _context.next = 4;
              return this._getLayoutSuggestionsViaWebSocket(components, layouts, context, cacheKey);
            case 4:
              return _context.abrupt("return", _context.sent);
            case 5:
              _context.prev = 5;
              _t = _context["catch"](3);
              if (shouldShowWarnings()) {
                console.warn('WebSocket request failed, falling back to HTTP:', _t);
              }
            case 6:
              _context.prev = 6;
              _context.next = 7;
              return fetch("".concat(this.baseUrl, "/layout-suggestions/"), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': this._getAuthHeader()
                },
                body: JSON.stringify({
                  components: components,
                  layouts: layouts,
                  context: context
                })
              });
            case 7:
              response = _context.sent;
              if (response.ok) {
                _context.next = 8;
                break;
              }
              throw new Error("HTTP error! status: ".concat(response.status));
            case 8:
              _context.next = 9;
              return response.json();
            case 9:
              data = _context.sent;
              // Cache the result
              this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
              });
              return _context.abrupt("return", data);
            case 10:
              _context.prev = 10;
              _t2 = _context["catch"](6);
              if (shouldShowWarnings()) {
                console.warn('AI service unavailable for layout suggestions:', _t2.message);
              }

              // Return fallback suggestions
              return _context.abrupt("return", this._getFallbackLayoutSuggestions(components));
            case 11:
            case "end":
              return _context.stop();
          }
        }, _callee, this, [[3, 5], [6, 10]]);
      }));
      function generateLayoutSuggestions(_x) {
        return _generateLayoutSuggestions.apply(this, arguments);
      }
      return generateLayoutSuggestions;
    }()
    /**
     * Get layout suggestions via WebSocket
     * @private
     */
    )
  }, {
    key: "_getLayoutSuggestionsViaWebSocket",
    value: (function () {
      var _getLayoutSuggestionsViaWebSocket2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(components, layouts, context, cacheKey) {
        var _this2 = this;
        return regenerator_default().wrap(function (_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              return _context2.abrupt("return", new Promise(function (resolve, reject) {
                var timeout = setTimeout(function () {
                  _this2.wsService.removeEventListener('layoutSuggestions', _responseHandler);
                  reject(new Error('WebSocket request timeout'));
                }, 10000); // 10 second timeout

                var _responseHandler = function responseHandler(suggestions) {
                  clearTimeout(timeout);
                  _this2.wsService.removeEventListener('layoutSuggestions', _responseHandler);
                  var data = {
                    suggestions: suggestions,
                    status: 'success'
                  };

                  // Cache the result
                  _this2.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                  });
                  resolve(data);
                };
                _this2.wsService.addEventListener('layoutSuggestions', _responseHandler);
                _this2.wsService.requestLayoutSuggestions(components, layouts, context);
              }));
            case 1:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }));
      function _getLayoutSuggestionsViaWebSocket(_x2, _x3, _x4, _x5) {
        return _getLayoutSuggestionsViaWebSocket2.apply(this, arguments);
      }
      return _getLayoutSuggestionsViaWebSocket;
    }()
    /**
     * Generate component combination suggestions
     * @param {Array} components - Current components in the app
     * @param {Object} selectedComponent - Currently selected component (optional)
     * @param {Object} context - Additional context (optional)
     * @returns {Promise<Object>} Component combination suggestions response
     */
    )
  }, {
    key: "generateComponentCombinations",
    value: (function () {
      var _generateComponentCombinations = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3(components) {
        var selectedComponent,
          context,
          cacheKey,
          cached,
          response,
          data,
          _args3 = arguments,
          _t3,
          _t4;
        return regenerator_default().wrap(function (_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              selectedComponent = _args3.length > 1 && _args3[1] !== undefined ? _args3[1] : null;
              context = _args3.length > 2 && _args3[2] !== undefined ? _args3[2] : {};
              if (this.enabled) {
                _context3.next = 1;
                break;
              }
              return _context3.abrupt("return", this._getFallbackCombinationSuggestions(components, selectedComponent));
            case 1:
              cacheKey = "combinations_".concat(JSON.stringify({
                components: components,
                selectedComponent: selectedComponent,
                context: context
              })); // Check cache first
              if (!this.cache.has(cacheKey)) {
                _context3.next = 2;
                break;
              }
              cached = this.cache.get(cacheKey);
              if (!(Date.now() - cached.timestamp < this.cacheTimeout)) {
                _context3.next = 2;
                break;
              }
              return _context3.abrupt("return", cached.data);
            case 2:
              if (!(this.useWebSocket && this.wsService.getStatus().connected)) {
                _context3.next = 6;
                break;
              }
              _context3.prev = 3;
              _context3.next = 4;
              return this._getComponentCombinationsViaWebSocket(components, selectedComponent, context, cacheKey);
            case 4:
              return _context3.abrupt("return", _context3.sent);
            case 5:
              _context3.prev = 5;
              _t3 = _context3["catch"](3);
              if (shouldShowWarnings()) {
                console.warn('WebSocket request failed, falling back to HTTP:', _t3);
              }
            case 6:
              _context3.prev = 6;
              _context3.next = 7;
              return fetch("".concat(this.baseUrl, "/component-combinations/"), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': this._getAuthHeader()
                },
                body: JSON.stringify({
                  components: components,
                  selected_component: selectedComponent,
                  context: context
                })
              });
            case 7:
              response = _context3.sent;
              if (response.ok) {
                _context3.next = 8;
                break;
              }
              throw new Error("HTTP error! status: ".concat(response.status));
            case 8:
              _context3.next = 9;
              return response.json();
            case 9:
              data = _context3.sent;
              // Cache the result
              this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
              });
              return _context3.abrupt("return", data);
            case 10:
              _context3.prev = 10;
              _t4 = _context3["catch"](6);
              if (shouldShowWarnings()) {
                console.warn('AI service unavailable for component combinations:', _t4.message);
              }

              // Return fallback suggestions
              return _context3.abrupt("return", this._getFallbackCombinationSuggestions(components, selectedComponent));
            case 11:
            case "end":
              return _context3.stop();
          }
        }, _callee3, this, [[3, 5], [6, 10]]);
      }));
      function generateComponentCombinations(_x6) {
        return _generateComponentCombinations.apply(this, arguments);
      }
      return generateComponentCombinations;
    }()
    /**
     * Get component combinations via WebSocket
     * @private
     */
    )
  }, {
    key: "_getComponentCombinationsViaWebSocket",
    value: (function () {
      var _getComponentCombinationsViaWebSocket2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4(components, selectedComponent, context, cacheKey) {
        var _this3 = this;
        return regenerator_default().wrap(function (_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              return _context4.abrupt("return", new Promise(function (resolve, reject) {
                var timeout = setTimeout(function () {
                  _this3.wsService.removeEventListener('componentCombinations', _responseHandler2);
                  reject(new Error('WebSocket request timeout'));
                }, 10000); // 10 second timeout

                var _responseHandler2 = function responseHandler(suggestions) {
                  clearTimeout(timeout);
                  _this3.wsService.removeEventListener('componentCombinations', _responseHandler2);
                  var data = {
                    suggestions: suggestions,
                    status: 'success'
                  };

                  // Cache the result
                  _this3.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                  });
                  resolve(data);
                };
                _this3.wsService.addEventListener('componentCombinations', _responseHandler2);
                _this3.wsService.requestComponentCombinations(components, selectedComponent, context);
              }));
            case 1:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }));
      function _getComponentCombinationsViaWebSocket(_x7, _x8, _x9, _x0) {
        return _getComponentCombinationsViaWebSocket2.apply(this, arguments);
      }
      return _getComponentCombinationsViaWebSocket;
    }()
    /**
     * Analyze app structure
     * @param {Array} components - Current components in the app
     * @param {Array} layouts - Existing layouts (optional)
     * @returns {Promise<Object>} App structure analysis response
     */
    )
  }, {
    key: "analyzeAppStructure",
    value: (function () {
      var _analyzeAppStructure = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee5(components) {
        var layouts,
          cacheKey,
          cached,
          response,
          data,
          fallbackData,
          _args5 = arguments,
          _t5;
        return regenerator_default().wrap(function (_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              layouts = _args5.length > 1 && _args5[1] !== undefined ? _args5[1] : [];
              if (this.enabled) {
                _context5.next = 1;
                break;
              }
              return _context5.abrupt("return", this._getBasicAnalysis(components));
            case 1:
              cacheKey = "analysis_".concat(JSON.stringify({
                components: components,
                layouts: layouts
              })); // Check cache first
              if (!this.cache.has(cacheKey)) {
                _context5.next = 2;
                break;
              }
              cached = this.cache.get(cacheKey);
              if (!(Date.now() - cached.timestamp < this.cacheTimeout)) {
                _context5.next = 2;
                break;
              }
              return _context5.abrupt("return", cached.data);
            case 2:
              _context5.prev = 2;
              _context5.next = 3;
              return fetch("".concat(this.baseUrl, "/analyze-structure/"), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': this._getAuthHeader()
                },
                body: JSON.stringify({
                  components: components,
                  layouts: layouts
                })
              });
            case 3:
              response = _context5.sent;
              if (response.ok) {
                _context5.next = 4;
                break;
              }
              throw new Error("HTTP error! status: ".concat(response.status));
            case 4:
              _context5.next = 5;
              return response.json();
            case 5:
              data = _context5.sent;
              // Cache the result
              this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
              });
              return _context5.abrupt("return", data);
            case 6:
              _context5.prev = 6;
              _t5 = _context5["catch"](2);
              console.warn('AI service unavailable, using basic analysis:', _t5.message);

              // Return basic analysis as fallback
              fallbackData = this._getBasicAnalysis(components); // Cache the fallback result for a shorter time
              this.cache.set(cacheKey, {
                data: fallbackData,
                timestamp: Date.now()
              });
              return _context5.abrupt("return", fallbackData);
            case 7:
            case "end":
              return _context5.stop();
          }
        }, _callee5, this, [[2, 6]]);
      }));
      function analyzeAppStructure(_x1) {
        return _analyzeAppStructure.apply(this, arguments);
      }
      return analyzeAppStructure;
    }()
    /**
     * Clear cache
     */
    )
  }, {
    key: "clearCache",
    value: function clearCache() {
      this.cache.clear();
    }

    /**
     * Get authentication header
     * @private
     */
  }, {
    key: "_getAuthHeader",
    value: function _getAuthHeader() {
      var token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      return token ? "Bearer ".concat(token) : '';
    }

    /**
     * Get fallback layout suggestions when API is unavailable
     * @private
     */
  }, {
    key: "_getFallbackLayoutSuggestions",
    value: function _getFallbackLayoutSuggestions(components) {
      var componentCount = components.length;
      var suggestions = [];
      if (componentCount <= 3) {
        suggestions.push({
          id: 'simple_flex',
          name: 'Simple Flexbox Layout',
          description: 'Basic vertical layout for simple apps',
          score: 80,
          explanation: 'Perfect for apps with few components',
          structure: {
            display: 'flex',
            flexDirection: 'column'
          }
        });
      }
      if (componentCount > 3) {
        suggestions.push({
          id: 'grid_layout',
          name: 'Grid Layout',
          description: 'Organized grid for multiple components',
          score: 85,
          explanation: 'Grid layout works well for organizing many components',
          structure: {
            display: 'grid',
            gap: '16px'
          }
        });
      }
      suggestions.push({
        id: 'header_footer',
        name: 'Header-Footer Layout',
        description: 'Classic layout with header and footer',
        score: 75,
        explanation: 'Traditional layout suitable for most applications',
        structure: {
          header: true,
          footer: true
        }
      });
      return {
        suggestions: suggestions,
        status: 'fallback',
        component_count: componentCount
      };
    }

    /**
     * Get fallback component combination suggestions
     * @private
     */
  }, {
    key: "_getFallbackCombinationSuggestions",
    value: function _getFallbackCombinationSuggestions(components, selectedComponent) {
      var suggestions = [];
      var componentTypes = components.map(function (c) {
        return c.type;
      });
      if (selectedComponent) {
        var type = selectedComponent.type;
        if (type === 'button' && !componentTypes.includes('form')) {
          suggestions.push({
            id: 'button_form',
            name: 'Button + Form',
            description: 'Add a form to go with your button',
            score: 70,
            components: ['button', 'form'],
            missing_components: ['form']
          });
        }
        if (type === 'text' && !componentTypes.includes('image')) {
          suggestions.push({
            id: 'text_image',
            name: 'Text + Image',
            description: 'Add an image to complement your text',
            score: 65,
            components: ['text', 'image'],
            missing_components: ['image']
          });
        }
      }
      if (!componentTypes.includes('header')) {
        suggestions.push({
          id: 'add_header',
          name: 'Add Header',
          description: 'Every app needs a header for navigation',
          score: 80,
          components: ['header'],
          missing_components: ['header']
        });
      }
      return {
        suggestions: suggestions,
        status: 'fallback',
        component_count: components.length
      };
    }

    /**
     * Get basic app structure analysis
     * @private
     */
  }, {
    key: "_getBasicAnalysis",
    value: function _getBasicAnalysis(components) {
      var componentTypes = {};
      components.forEach(function (comp) {
        var type = comp.type || 'unknown';
        componentTypes[type] = (componentTypes[type] || 0) + 1;
      });
      return {
        analysis: {
          component_count: components.length,
          component_types: componentTypes,
          has_navigation: Object.keys(componentTypes).some(function (type) {
            return ['header', 'nav', 'menu'].includes(type);
          }),
          has_forms: Object.keys(componentTypes).some(function (type) {
            return ['form', 'input', 'button'].includes(type);
          }),
          has_media: Object.keys(componentTypes).some(function (type) {
            return ['image', 'video', 'gallery'].includes(type);
          }),
          complexity_score: Object.keys(componentTypes).length * 2 + components.length,
          app_type: 'general'
        },
        status: 'basic'
      };
    }
  }]);
}(); // Create and export singleton instance
var aiDesignService = new AIDesignService();
/* harmony default export */ const services_aiDesignService = (aiDesignService);

/***/ })

}]);