"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3364],{

/***/ 8144:
/***/ (() => {

// extracted by mini-css-extract-plugin


/***/ }),

/***/ 26051:
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _default = exports["default"] = {
  "code[class*=\"language-\"]": {
    "color": "#c5c8c6",
    "textShadow": "0 1px rgba(0, 0, 0, 0.3)",
    "fontFamily": "Inconsolata, Monaco, Consolas, 'Courier New', Courier, monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#c5c8c6",
    "textShadow": "0 1px rgba(0, 0, 0, 0.3)",
    "fontFamily": "Inconsolata, Monaco, Consolas, 'Courier New', Courier, monospace",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto",
    "borderRadius": "0.3em",
    "background": "#1d1f21"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "background": "#1d1f21",
    "padding": ".1em",
    "borderRadius": ".3em"
  },
  "comment": {
    "color": "#7C7C7C"
  },
  "prolog": {
    "color": "#7C7C7C"
  },
  "doctype": {
    "color": "#7C7C7C"
  },
  "cdata": {
    "color": "#7C7C7C"
  },
  "punctuation": {
    "color": "#c5c8c6"
  },
  ".namespace": {
    "Opacity": ".7"
  },
  "property": {
    "color": "#96CBFE"
  },
  "keyword": {
    "color": "#96CBFE"
  },
  "tag": {
    "color": "#96CBFE"
  },
  "class-name": {
    "color": "#FFFFB6",
    "textDecoration": "underline"
  },
  "boolean": {
    "color": "#99CC99"
  },
  "constant": {
    "color": "#99CC99"
  },
  "symbol": {
    "color": "#f92672"
  },
  "deleted": {
    "color": "#f92672"
  },
  "number": {
    "color": "#FF73FD"
  },
  "selector": {
    "color": "#A8FF60"
  },
  "attr-name": {
    "color": "#A8FF60"
  },
  "string": {
    "color": "#A8FF60"
  },
  "char": {
    "color": "#A8FF60"
  },
  "builtin": {
    "color": "#A8FF60"
  },
  "inserted": {
    "color": "#A8FF60"
  },
  "variable": {
    "color": "#C6C5FE"
  },
  "operator": {
    "color": "#EDEDED"
  },
  "entity": {
    "color": "#FFFFB6",
    "cursor": "help"
  },
  "url": {
    "color": "#96CBFE"
  },
  ".language-css .token.string": {
    "color": "#87C38A"
  },
  ".style .token.string": {
    "color": "#87C38A"
  },
  "atrule": {
    "color": "#F9EE98"
  },
  "attr-value": {
    "color": "#F9EE98"
  },
  "function": {
    "color": "#DAD085"
  },
  "regex": {
    "color": "#E9C062"
  },
  "important": {
    "color": "#fd971f",
    "fontWeight": "bold"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  }
};

/***/ }),

/***/ 45708:
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {


/*
React-Quill
https://github.com/zenoamaro/react-quill
*/
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var react_1 = __importDefault(__webpack_require__(96540));
var react_dom_1 = __importDefault(__webpack_require__(40961));
var isEqual_1 = __importDefault(__webpack_require__(2404));
var quill_1 = __importDefault(__webpack_require__(91574));
var ReactQuill = /** @class */ (function (_super) {
    __extends(ReactQuill, _super);
    function ReactQuill(props) {
        var _this = _super.call(this, props) || this;
        /*
        Changing one of these props should cause a full re-render and a
        re-instantiation of the Quill editor.
        */
        _this.dirtyProps = [
            'modules',
            'formats',
            'bounds',
            'theme',
            'children',
        ];
        /*
        Changing one of these props should cause a regular update. These are mostly
        props that act on the container, rather than the quillized editing area.
        */
        _this.cleanProps = [
            'id',
            'className',
            'style',
            'placeholder',
            'tabIndex',
            'onChange',
            'onChangeSelection',
            'onFocus',
            'onBlur',
            'onKeyPress',
            'onKeyDown',
            'onKeyUp',
        ];
        _this.state = {
            generation: 0,
        };
        /*
        Tracks the internal selection of the Quill editor
        */
        _this.selection = null;
        _this.onEditorChange = function (eventName, rangeOrDelta, oldRangeOrDelta, source) {
            var _a, _b, _c, _d;
            if (eventName === 'text-change') {
                (_b = (_a = _this).onEditorChangeText) === null || _b === void 0 ? void 0 : _b.call(_a, _this.editor.root.innerHTML, rangeOrDelta, source, _this.unprivilegedEditor);
            }
            else if (eventName === 'selection-change') {
                (_d = (_c = _this).onEditorChangeSelection) === null || _d === void 0 ? void 0 : _d.call(_c, rangeOrDelta, source, _this.unprivilegedEditor);
            }
        };
        var value = _this.isControlled() ? props.value : props.defaultValue;
        _this.value = (value !== null && value !== void 0 ? value : '');
        return _this;
    }
    ReactQuill.prototype.validateProps = function (props) {
        var _a;
        if (react_1.default.Children.count(props.children) > 1)
            throw new Error('The Quill editing area can only be composed of a single React element.');
        if (react_1.default.Children.count(props.children)) {
            var child = react_1.default.Children.only(props.children);
            if (((_a = child) === null || _a === void 0 ? void 0 : _a.type) === 'textarea')
                throw new Error('Quill does not support editing on a <textarea>. Use a <div> instead.');
        }
        if (this.lastDeltaChangeSet &&
            props.value === this.lastDeltaChangeSet)
            throw new Error('You are passing the `delta` object from the `onChange` event back ' +
                'as `value`. You most probably want `editor.getContents()` instead. ' +
                'See: https://github.com/zenoamaro/react-quill#using-deltas');
    };
    ReactQuill.prototype.shouldComponentUpdate = function (nextProps, nextState) {
        var _this = this;
        var _a;
        this.validateProps(nextProps);
        // If the editor hasn't been instantiated yet, or the component has been
        // regenerated, we already know we should update.
        if (!this.editor || this.state.generation !== nextState.generation) {
            return true;
        }
        // Handle value changes in-place
        if ('value' in nextProps) {
            var prevContents = this.getEditorContents();
            var nextContents = (_a = nextProps.value, (_a !== null && _a !== void 0 ? _a : ''));
            // NOTE: Seeing that Quill is missing a way to prevent edits, we have to
            //       settle for a hybrid between controlled and uncontrolled mode. We
            //       can't prevent the change, but we'll still override content
            //       whenever `value` differs from current state.
            // NOTE: Comparing an HTML string and a Quill Delta will always trigger a
            //       change, regardless of whether they represent the same document.
            if (!this.isEqualValue(nextContents, prevContents)) {
                this.setEditorContents(this.editor, nextContents);
            }
        }
        // Handle read-only changes in-place
        if (nextProps.readOnly !== this.props.readOnly) {
            this.setEditorReadOnly(this.editor, nextProps.readOnly);
        }
        // Clean and Dirty props require a render
        return __spreadArrays(this.cleanProps, this.dirtyProps).some(function (prop) {
            return !isEqual_1.default(nextProps[prop], _this.props[prop]);
        });
    };
    ReactQuill.prototype.shouldComponentRegenerate = function (nextProps) {
        var _this = this;
        // Whenever a `dirtyProp` changes, the editor needs reinstantiation.
        return this.dirtyProps.some(function (prop) {
            return !isEqual_1.default(nextProps[prop], _this.props[prop]);
        });
    };
    ReactQuill.prototype.componentDidMount = function () {
        this.instantiateEditor();
        this.setEditorContents(this.editor, this.getEditorContents());
    };
    ReactQuill.prototype.componentWillUnmount = function () {
        this.destroyEditor();
    };
    ReactQuill.prototype.componentDidUpdate = function (prevProps, prevState) {
        var _this = this;
        // If we're changing one of the `dirtyProps`, the entire Quill Editor needs
        // to be re-instantiated. Regenerating the editor will cause the whole tree,
        // including the container, to be cleaned up and re-rendered from scratch.
        // Store the contents so they can be restored later.
        if (this.editor && this.shouldComponentRegenerate(prevProps)) {
            var delta = this.editor.getContents();
            var selection = this.editor.getSelection();
            this.regenerationSnapshot = { delta: delta, selection: selection };
            this.setState({ generation: this.state.generation + 1 });
            this.destroyEditor();
        }
        // The component has been regenerated, so it must be re-instantiated, and
        // its content must be restored to the previous values from the snapshot.
        if (this.state.generation !== prevState.generation) {
            var _a = this.regenerationSnapshot, delta = _a.delta, selection_1 = _a.selection;
            delete this.regenerationSnapshot;
            this.instantiateEditor();
            var editor_1 = this.editor;
            editor_1.setContents(delta);
            postpone(function () { return _this.setEditorSelection(editor_1, selection_1); });
        }
    };
    ReactQuill.prototype.instantiateEditor = function () {
        if (this.editor) {
            this.hookEditor(this.editor);
        }
        else {
            this.editor = this.createEditor(this.getEditingArea(), this.getEditorConfig());
        }
    };
    ReactQuill.prototype.destroyEditor = function () {
        if (!this.editor)
            return;
        this.unhookEditor(this.editor);
    };
    /*
    We consider the component to be controlled if `value` is being sent in props.
    */
    ReactQuill.prototype.isControlled = function () {
        return 'value' in this.props;
    };
    ReactQuill.prototype.getEditorConfig = function () {
        return {
            bounds: this.props.bounds,
            formats: this.props.formats,
            modules: this.props.modules,
            placeholder: this.props.placeholder,
            readOnly: this.props.readOnly,
            scrollingContainer: this.props.scrollingContainer,
            tabIndex: this.props.tabIndex,
            theme: this.props.theme,
        };
    };
    ReactQuill.prototype.getEditor = function () {
        if (!this.editor)
            throw new Error('Accessing non-instantiated editor');
        return this.editor;
    };
    /**
    Creates an editor on the given element. The editor will be passed the
    configuration, have its events bound,
    */
    ReactQuill.prototype.createEditor = function (element, config) {
        var editor = new quill_1.default(element, config);
        if (config.tabIndex != null) {
            this.setEditorTabIndex(editor, config.tabIndex);
        }
        this.hookEditor(editor);
        return editor;
    };
    ReactQuill.prototype.hookEditor = function (editor) {
        // Expose the editor on change events via a weaker, unprivileged proxy
        // object that does not allow accidentally modifying editor state.
        this.unprivilegedEditor = this.makeUnprivilegedEditor(editor);
        // Using `editor-change` allows picking up silent updates, like selection
        // changes on typing.
        editor.on('editor-change', this.onEditorChange);
    };
    ReactQuill.prototype.unhookEditor = function (editor) {
        editor.off('editor-change', this.onEditorChange);
    };
    ReactQuill.prototype.getEditorContents = function () {
        return this.value;
    };
    ReactQuill.prototype.getEditorSelection = function () {
        return this.selection;
    };
    /*
    True if the value is a Delta instance or a Delta look-alike.
    */
    ReactQuill.prototype.isDelta = function (value) {
        return value && value.ops;
    };
    /*
    Special comparison function that knows how to compare Deltas.
    */
    ReactQuill.prototype.isEqualValue = function (value, nextValue) {
        if (this.isDelta(value) && this.isDelta(nextValue)) {
            return isEqual_1.default(value.ops, nextValue.ops);
        }
        else {
            return isEqual_1.default(value, nextValue);
        }
    };
    /*
    Replace the contents of the editor, but keep the previous selection hanging
    around so that the cursor won't move.
    */
    ReactQuill.prototype.setEditorContents = function (editor, value) {
        var _this = this;
        this.value = value;
        var sel = this.getEditorSelection();
        if (typeof value === 'string') {
            editor.setContents(editor.clipboard.convert(value));
        }
        else {
            editor.setContents(value);
        }
        postpone(function () { return _this.setEditorSelection(editor, sel); });
    };
    ReactQuill.prototype.setEditorSelection = function (editor, range) {
        this.selection = range;
        if (range) {
            // Validate bounds before applying.
            var length_1 = editor.getLength();
            range.index = Math.max(0, Math.min(range.index, length_1 - 1));
            range.length = Math.max(0, Math.min(range.length, (length_1 - 1) - range.index));
            editor.setSelection(range);
        }
    };
    ReactQuill.prototype.setEditorTabIndex = function (editor, tabIndex) {
        var _a, _b;
        if ((_b = (_a = editor) === null || _a === void 0 ? void 0 : _a.scroll) === null || _b === void 0 ? void 0 : _b.domNode) {
            editor.scroll.domNode.tabIndex = tabIndex;
        }
    };
    ReactQuill.prototype.setEditorReadOnly = function (editor, value) {
        if (value) {
            editor.disable();
        }
        else {
            editor.enable();
        }
    };
    /*
    Returns a weaker, unprivileged proxy object that only exposes read-only
    accessors found on the editor instance, without any state-modifying methods.
    */
    ReactQuill.prototype.makeUnprivilegedEditor = function (editor) {
        var e = editor;
        return {
            getHTML: function () { return e.root.innerHTML; },
            getLength: e.getLength.bind(e),
            getText: e.getText.bind(e),
            getContents: e.getContents.bind(e),
            getSelection: e.getSelection.bind(e),
            getBounds: e.getBounds.bind(e),
        };
    };
    ReactQuill.prototype.getEditingArea = function () {
        if (!this.editingArea) {
            throw new Error('Instantiating on missing editing area');
        }
        var element = react_dom_1.default.findDOMNode(this.editingArea);
        if (!element) {
            throw new Error('Cannot find element for editing area');
        }
        if (element.nodeType === 3) {
            throw new Error('Editing area cannot be a text node');
        }
        return element;
    };
    /*
    Renders an editor area, unless it has been provided one to clone.
    */
    ReactQuill.prototype.renderEditingArea = function () {
        var _this = this;
        var _a = this.props, children = _a.children, preserveWhitespace = _a.preserveWhitespace;
        var generation = this.state.generation;
        var properties = {
            key: generation,
            ref: function (instance) {
                _this.editingArea = instance;
            },
        };
        if (react_1.default.Children.count(children)) {
            return react_1.default.cloneElement(react_1.default.Children.only(children), properties);
        }
        return preserveWhitespace ?
            react_1.default.createElement("pre", __assign({}, properties)) :
            react_1.default.createElement("div", __assign({}, properties));
    };
    ReactQuill.prototype.render = function () {
        var _a;
        return (react_1.default.createElement("div", { id: this.props.id, style: this.props.style, key: this.state.generation, className: "quill " + (_a = this.props.className, (_a !== null && _a !== void 0 ? _a : '')), onKeyPress: this.props.onKeyPress, onKeyDown: this.props.onKeyDown, onKeyUp: this.props.onKeyUp }, this.renderEditingArea()));
    };
    ReactQuill.prototype.onEditorChangeText = function (value, delta, source, editor) {
        var _a, _b;
        if (!this.editor)
            return;
        // We keep storing the same type of value as what the user gives us,
        // so that value comparisons will be more stable and predictable.
        var nextContents = this.isDelta(this.value)
            ? editor.getContents()
            : editor.getHTML();
        if (nextContents !== this.getEditorContents()) {
            // Taint this `delta` object, so we can recognize whether the user
            // is trying to send it back as `value`, preventing a likely loop.
            this.lastDeltaChangeSet = delta;
            this.value = nextContents;
            (_b = (_a = this.props).onChange) === null || _b === void 0 ? void 0 : _b.call(_a, value, delta, source, editor);
        }
    };
    ReactQuill.prototype.onEditorChangeSelection = function (nextSelection, source, editor) {
        var _a, _b, _c, _d, _e, _f;
        if (!this.editor)
            return;
        var currentSelection = this.getEditorSelection();
        var hasGainedFocus = !currentSelection && nextSelection;
        var hasLostFocus = currentSelection && !nextSelection;
        if (isEqual_1.default(nextSelection, currentSelection))
            return;
        this.selection = nextSelection;
        (_b = (_a = this.props).onChangeSelection) === null || _b === void 0 ? void 0 : _b.call(_a, nextSelection, source, editor);
        if (hasGainedFocus) {
            (_d = (_c = this.props).onFocus) === null || _d === void 0 ? void 0 : _d.call(_c, nextSelection, source, editor);
        }
        else if (hasLostFocus) {
            (_f = (_e = this.props).onBlur) === null || _f === void 0 ? void 0 : _f.call(_e, currentSelection, source, editor);
        }
    };
    ReactQuill.prototype.focus = function () {
        if (!this.editor)
            return;
        this.editor.focus();
    };
    ReactQuill.prototype.blur = function () {
        if (!this.editor)
            return;
        this.selection = null;
        this.editor.blur();
    };
    ReactQuill.displayName = 'React Quill';
    /*
    Export Quill to be able to call `register`
    */
    ReactQuill.Quill = quill_1.default;
    ReactQuill.defaultProps = {
        theme: 'snow',
        modules: {},
        readOnly: false,
    };
    return ReactQuill;
}(react_1.default.Component));
/*
Small helper to execute a function in the next micro-tick.
*/
function postpone(fn) {
    Promise.resolve().then(fn);
}
module.exports = ReactQuill;
//# sourceMappingURL=index.js.map

/***/ }),

/***/ 61844:
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _default = exports["default"] = {
  "code[class*=\"language-\"]": {
    "color": "#f8f8f2",
    "background": "none",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#f8f8f2",
    "background": "#2b2b2b",
    "fontFamily": "Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "wordWrap": "normal",
    "lineHeight": "1.5",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "1em",
    "margin": "0.5em 0",
    "overflow": "auto",
    "borderRadius": "0.3em"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "background": "#2b2b2b",
    "padding": "0.1em",
    "borderRadius": "0.3em",
    "whiteSpace": "normal"
  },
  "comment": {
    "color": "#d4d0ab"
  },
  "prolog": {
    "color": "#d4d0ab"
  },
  "doctype": {
    "color": "#d4d0ab"
  },
  "cdata": {
    "color": "#d4d0ab"
  },
  "punctuation": {
    "color": "#fefefe"
  },
  "property": {
    "color": "#ffa07a"
  },
  "tag": {
    "color": "#ffa07a"
  },
  "constant": {
    "color": "#ffa07a"
  },
  "symbol": {
    "color": "#ffa07a"
  },
  "deleted": {
    "color": "#ffa07a"
  },
  "boolean": {
    "color": "#00e0e0"
  },
  "number": {
    "color": "#00e0e0"
  },
  "selector": {
    "color": "#abe338"
  },
  "attr-name": {
    "color": "#abe338"
  },
  "string": {
    "color": "#abe338"
  },
  "char": {
    "color": "#abe338"
  },
  "builtin": {
    "color": "#abe338"
  },
  "inserted": {
    "color": "#abe338"
  },
  "operator": {
    "color": "#00e0e0"
  },
  "entity": {
    "color": "#00e0e0",
    "cursor": "help"
  },
  "url": {
    "color": "#00e0e0"
  },
  ".language-css .token.string": {
    "color": "#00e0e0"
  },
  ".style .token.string": {
    "color": "#00e0e0"
  },
  "variable": {
    "color": "#00e0e0"
  },
  "atrule": {
    "color": "#ffd700"
  },
  "attr-value": {
    "color": "#ffd700"
  },
  "function": {
    "color": "#ffd700"
  },
  "keyword": {
    "color": "#00e0e0"
  },
  "regex": {
    "color": "#ffd700"
  },
  "important": {
    "color": "#ffd700",
    "fontWeight": "bold"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "italic": {
    "fontStyle": "italic"
  }
};

/***/ }),

/***/ 94974:
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _default = exports["default"] = {
  "code[class*=\"language-\"]": {
    "color": "#fff",
    "textShadow": "0 1px 1px #000",
    "fontFamily": "Menlo, Monaco, \"Courier New\", monospace",
    "direction": "ltr",
    "textAlign": "left",
    "wordSpacing": "normal",
    "whiteSpace": "pre",
    "wordWrap": "normal",
    "lineHeight": "1.4",
    "background": "none",
    "border": "0",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none"
  },
  "pre[class*=\"language-\"]": {
    "color": "#fff",
    "textShadow": "0 1px 1px #000",
    "fontFamily": "Menlo, Monaco, \"Courier New\", monospace",
    "direction": "ltr",
    "textAlign": "left",
    "wordSpacing": "normal",
    "whiteSpace": "pre",
    "wordWrap": "normal",
    "lineHeight": "1.4",
    "background": "#222",
    "border": "0",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "padding": "15px",
    "margin": "1em 0",
    "overflow": "auto",
    "MozBorderRadius": "8px",
    "WebkitBorderRadius": "8px",
    "borderRadius": "8px"
  },
  "pre[class*=\"language-\"] code": {
    "float": "left",
    "padding": "0 15px 0 0"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "background": "#222",
    "padding": "5px 10px",
    "lineHeight": "1",
    "MozBorderRadius": "3px",
    "WebkitBorderRadius": "3px",
    "borderRadius": "3px"
  },
  "comment": {
    "color": "#797979"
  },
  "prolog": {
    "color": "#797979"
  },
  "doctype": {
    "color": "#797979"
  },
  "cdata": {
    "color": "#797979"
  },
  "selector": {
    "color": "#fff"
  },
  "operator": {
    "color": "#fff"
  },
  "punctuation": {
    "color": "#fff"
  },
  "namespace": {
    "Opacity": ".7"
  },
  "tag": {
    "color": "#ffd893"
  },
  "boolean": {
    "color": "#ffd893"
  },
  "atrule": {
    "color": "#B0C975"
  },
  "attr-value": {
    "color": "#B0C975"
  },
  "hex": {
    "color": "#B0C975"
  },
  "string": {
    "color": "#B0C975"
  },
  "property": {
    "color": "#c27628"
  },
  "entity": {
    "color": "#c27628",
    "cursor": "help"
  },
  "url": {
    "color": "#c27628"
  },
  "attr-name": {
    "color": "#c27628"
  },
  "keyword": {
    "color": "#c27628"
  },
  "regex": {
    "color": "#9B71C6"
  },
  "function": {
    "color": "#e5a638"
  },
  "constant": {
    "color": "#e5a638"
  },
  "variable": {
    "color": "#fdfba8"
  },
  "number": {
    "color": "#8799B0"
  },
  "important": {
    "color": "#E45734"
  },
  "deliminator": {
    "color": "#E45734"
  },
  ".line-highlight.line-highlight": {
    "background": "rgba(255, 255, 255, .2)"
  },
  ".line-highlight.line-highlight:before": {
    "top": ".3em",
    "backgroundColor": "rgba(255, 255, 255, .3)",
    "color": "#fff",
    "MozBorderRadius": "8px",
    "WebkitBorderRadius": "8px",
    "borderRadius": "8px"
  },
  ".line-highlight.line-highlight[data-end]:after": {
    "top": ".3em",
    "backgroundColor": "rgba(255, 255, 255, .3)",
    "color": "#fff",
    "MozBorderRadius": "8px",
    "WebkitBorderRadius": "8px",
    "borderRadius": "8px"
  },
  ".line-numbers .line-numbers-rows > span": {
    "borderRight": "3px #d9d336 solid"
  }
};

/***/ }),

/***/ 96485:
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _default = exports["default"] = {
  "code[class*=\"language-\"]": {
    "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
    "fontSize": "14px",
    "lineHeight": "1.375",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "background": "#f5f7ff",
    "color": "#5e6687"
  },
  "pre[class*=\"language-\"]": {
    "fontFamily": "Consolas, Menlo, Monaco, \"Andale Mono WT\", \"Andale Mono\", \"Lucida Console\", \"Lucida Sans Typewriter\", \"DejaVu Sans Mono\", \"Bitstream Vera Sans Mono\", \"Liberation Mono\", \"Nimbus Mono L\", \"Courier New\", Courier, monospace",
    "fontSize": "14px",
    "lineHeight": "1.375",
    "direction": "ltr",
    "textAlign": "left",
    "whiteSpace": "pre",
    "wordSpacing": "normal",
    "wordBreak": "normal",
    "MozTabSize": "4",
    "OTabSize": "4",
    "tabSize": "4",
    "WebkitHyphens": "none",
    "MozHyphens": "none",
    "msHyphens": "none",
    "hyphens": "none",
    "background": "#f5f7ff",
    "color": "#5e6687",
    "padding": "1em",
    "margin": ".5em 0",
    "overflow": "auto"
  },
  "pre > code[class*=\"language-\"]": {
    "fontSize": "1em"
  },
  "pre[class*=\"language-\"]::-moz-selection": {
    "textShadow": "none",
    "background": "#dfe2f1"
  },
  "pre[class*=\"language-\"] ::-moz-selection": {
    "textShadow": "none",
    "background": "#dfe2f1"
  },
  "code[class*=\"language-\"]::-moz-selection": {
    "textShadow": "none",
    "background": "#dfe2f1"
  },
  "code[class*=\"language-\"] ::-moz-selection": {
    "textShadow": "none",
    "background": "#dfe2f1"
  },
  "pre[class*=\"language-\"]::selection": {
    "textShadow": "none",
    "background": "#dfe2f1"
  },
  "pre[class*=\"language-\"] ::selection": {
    "textShadow": "none",
    "background": "#dfe2f1"
  },
  "code[class*=\"language-\"]::selection": {
    "textShadow": "none",
    "background": "#dfe2f1"
  },
  "code[class*=\"language-\"] ::selection": {
    "textShadow": "none",
    "background": "#dfe2f1"
  },
  ":not(pre) > code[class*=\"language-\"]": {
    "padding": ".1em",
    "borderRadius": ".3em"
  },
  "comment": {
    "color": "#898ea4"
  },
  "prolog": {
    "color": "#898ea4"
  },
  "doctype": {
    "color": "#898ea4"
  },
  "cdata": {
    "color": "#898ea4"
  },
  "punctuation": {
    "color": "#5e6687"
  },
  "namespace": {
    "Opacity": ".7"
  },
  "operator": {
    "color": "#c76b29"
  },
  "boolean": {
    "color": "#c76b29"
  },
  "number": {
    "color": "#c76b29"
  },
  "property": {
    "color": "#c08b30"
  },
  "tag": {
    "color": "#3d8fd1"
  },
  "string": {
    "color": "#22a2c9"
  },
  "selector": {
    "color": "#6679cc"
  },
  "attr-name": {
    "color": "#c76b29"
  },
  "entity": {
    "color": "#22a2c9",
    "cursor": "help"
  },
  "url": {
    "color": "#22a2c9"
  },
  ".language-css .token.string": {
    "color": "#22a2c9"
  },
  ".style .token.string": {
    "color": "#22a2c9"
  },
  "attr-value": {
    "color": "#ac9739"
  },
  "keyword": {
    "color": "#ac9739"
  },
  "control": {
    "color": "#ac9739"
  },
  "directive": {
    "color": "#ac9739"
  },
  "unit": {
    "color": "#ac9739"
  },
  "statement": {
    "color": "#22a2c9"
  },
  "regex": {
    "color": "#22a2c9"
  },
  "atrule": {
    "color": "#22a2c9"
  },
  "placeholder": {
    "color": "#3d8fd1"
  },
  "variable": {
    "color": "#3d8fd1"
  },
  "deleted": {
    "textDecoration": "line-through"
  },
  "inserted": {
    "borderBottom": "1px dotted #202746",
    "textDecoration": "none"
  },
  "italic": {
    "fontStyle": "italic"
  },
  "important": {
    "fontWeight": "bold",
    "color": "#c94922"
  },
  "bold": {
    "fontWeight": "bold"
  },
  "pre > code.highlight": {
    "Outline": "0.4em solid #c94922",
    "OutlineOffset": ".4em"
  },
  ".line-numbers.line-numbers .line-numbers-rows": {
    "borderRightColor": "#dfe2f1"
  },
  ".line-numbers .line-numbers-rows > span:before": {
    "color": "#979db4"
  },
  ".line-highlight.line-highlight": {
    "background": "linear-gradient(to right, rgba(107, 115, 148, 0.2) 70%, rgba(107, 115, 148, 0))"
  }
};

/***/ })

}]);